package cloud.demand.lab.modules.operation_view.inventory_health.dto.actual;

import java.time.LocalDate;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
public class TrendGraphReq implements Cloneable {
    /**
     * 统计日期
     */
    @NotNull
    LocalDate date;

    /**
     * 开始日期
     */
    @NotNull
    LocalDate beginDate;

    /**
     * 结束日期
     */
    @NotNull
    LocalDate endDate;

    /**
     * 日期区间类型  'day,week'
     */
    String dateRange = "day";

    /**
     * 图表定义 @See GraphCard
     */
    @NotNull
    GraphCard cardType;

    /**
     * 可用区
     */
    @NotNull
    private List<String> zoneName;
    /**
     * 实例类型
     */
    @NotNull
    private List<String> instanceType;

    /**
     * 库存类型：线上库存、线下搬迁、线下流转
     */
    private List<String> lineType;

    /**
     * 物料类型：好料、差料、呆料
     */
    private List<String> materialType;

    /**
     * 库存细类：用户预扣、小核库存、大核库存、大核预留等等
     */
    private List<String> invDetailType;

    /**
     * 服务水平-需求类型 【包年包月/弹性/全部】
     */
    private String slaType;
    /**
     * 供应情况-采购时间类型 [期望交付时间/产品提货时间/交付时间]
     */
    private String supplyPurchaseType;
    /**
     * 供应情况-搬迁时间类型 [计划时间/交付时间]
     */
    private String supplyMoveType;
    /**
     * 客户类型
     */
    private String customerCustomGroup;

    /**
     * 客户预扣剔除，如果为 true，返回的库存数据剔除预扣，预扣数据不计入库存
     */
    private Boolean isIgnoreReserved;

    /**
     * 计费规模/服务规模/全部
     */
    private String scaleType;

    /**
     * 剔除名单 可用区+实例类型 用于计算服务水平
     */
    private List<Item> ignore;

    /**
     *售罄率类型：全时段，闲时，忙时
     */
    private String soldType;

    /**
     * 供应情况-采购时间类型 [期望交付时间/产品提货时间/交付时间/预计到货时间] 到采购时间字段的映射
     * @return
     */
    public String supplyPurchaseTypeToTimeField() {
        if (supplyPurchaseType.equals("期望交付时间")) {
            return "quota_use_time";
        } else if (supplyPurchaseType.equals("产品提货时间")) {
            return "cloud_delivery_time";
        } else if (supplyPurchaseType.equals("预计到货时间")) {
            return "est_arrive_date";
        } else {
            return "erp_actual_date";
        }
    }

    /**
     * 供应情况-搬迁时间类型 [计划时间/交付时间] 到搬迁时间字段的映射
     * @return
     */
    public String supplyMoveTypeToTimeField() {
        if (supplyMoveType.equals("计划时间")) {
            return "AffirmFinishTime";
        } else {
            return "FinishTime";
        }
    }

    public TrendGraphReq clone() {
        try {
            return (TrendGraphReq) super.clone();
        } catch (CloneNotSupportedException e) {
            e.printStackTrace();
        }
        TrendGraphReq trendGraphReq = new TrendGraphReq();
        // 复制所有字段
        trendGraphReq.setDate(this.date);
        trendGraphReq.setBeginDate(this.beginDate);
        trendGraphReq.setEndDate(this.endDate);
        trendGraphReq.setDateRange(this.dateRange);
        trendGraphReq.setCardType(this.cardType);
        trendGraphReq.setZoneName(this.zoneName);
        trendGraphReq.setInstanceType(this.instanceType);
        trendGraphReq.setLineType(this.lineType);
        trendGraphReq.setMaterialType(this.materialType);
        trendGraphReq.setInvDetailType(this.invDetailType);
        trendGraphReq.setSlaType(this.slaType);
        trendGraphReq.setSupplyPurchaseType(this.supplyPurchaseType);
        trendGraphReq.setSupplyMoveType(this.supplyMoveType);
        trendGraphReq.setCustomerCustomGroup(this.customerCustomGroup);
        trendGraphReq.setIsIgnoreReserved(this.isIgnoreReserved);
        trendGraphReq.setScaleType(this.scaleType);
        return trendGraphReq;
    }

    @Data
    public static class Item {
        public String zoneName;
        public String instanceType;
    }
}
