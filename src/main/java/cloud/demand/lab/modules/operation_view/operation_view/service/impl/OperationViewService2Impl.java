package cloud.demand.lab.modules.operation_view.operation_view.service.impl;

import cloud.demand.lab.common.task_log.service.TaskLogService;
import cloud.demand.lab.common.utils.AmountUtils;
import cloud.demand.lab.common.utils.ORMUtils;
import cloud.demand.lab.common.utils.ORMUtils.WhereContent;
import cloud.demand.lab.common.utils.SpringUtil;
import cloud.demand.lab.modules.common_dict.DO.ResPlanHolidayWeekDO;
import cloud.demand.lab.modules.common_dict.service.DictService;
import cloud.demand.lab.modules.common_dict.service.impl.DictServiceImpl;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.BufferAverageCoreDTO;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.HolidayWeekInfoDTO;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.config.QueryInstanceTypeConfigReq;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.config.QueryZoneConfigReq;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.DwsInventoryHealthWeeklyDeliveryDfDO;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.DwsInventoryHealthWeeklyScaleDfDO;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.InventoryHealthMainInstanceTypeConfigDO;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.InventoryHealthMainZoneNameConfigDO;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.InventoryHealthZlkhbSafetyInventorySnapshotDO;
import cloud.demand.lab.modules.operation_view.inventory_health.enums.CustomerCustomGroupEnum;
import cloud.demand.lab.modules.operation_view.inventory_health.service.InventoryHealthConfigService;
import cloud.demand.lab.modules.operation_view.inventory_health.service.InventoryHealthDictService;
import cloud.demand.lab.modules.operation_view.inventory_health.service.impl.InventoryHealthConfigServiceImpl;
import cloud.demand.lab.modules.operation_view.inventory_health.service.impl.InventoryTurnoverServiceImpl;
import cloud.demand.lab.modules.operation_view.operation_view.config.DynamicProperties;
import cloud.demand.lab.modules.operation_view.operation_view.entity.DwsActualInventoryDfDO;
import cloud.demand.lab.modules.operation_view.operation_view.entity.DwsBufferSafeInventoryDfDO;
import cloud.demand.lab.modules.operation_view.operation_view.entity.DwsDemandWeekNPplVersionItemDO;
import cloud.demand.lab.modules.operation_view.operation_view.entity.DwsInventoryHealthMckTurnoverWfDO;
import cloud.demand.lab.modules.operation_view.operation_view.entity.InstanceModelScaleDO;
import cloud.demand.lab.modules.operation_view.operation_view.entity.SlaResultItem;
import cloud.demand.lab.modules.operation_view.operation_view.model.ActualInventoryListReq;
import cloud.demand.lab.modules.operation_view.operation_view.model.BufferScaleDTO;
import cloud.demand.lab.modules.operation_view.operation_view.model.EffectiveAlgorithmDTO;
import cloud.demand.lab.modules.operation_view.operation_view.model.HistoryWeekPeakDemandItemDTO;
import cloud.demand.lab.modules.operation_view.operation_view.model.OperationViewExternalReq2;
import cloud.demand.lab.modules.operation_view.operation_view.model.OperationViewExternalResp2;
import cloud.demand.lab.modules.operation_view.operation_view.model.OperationViewInstanceModelReq;
import cloud.demand.lab.modules.operation_view.operation_view.model.OperationViewInstanceModelResp;
import cloud.demand.lab.modules.operation_view.operation_view.model.OperationViewReq2;
import cloud.demand.lab.modules.operation_view.operation_view.model.OperationViewResp2;
import cloud.demand.lab.modules.operation_view.operation_view.model.OperationViewResp2.InventoryForecastInfo;
import cloud.demand.lab.modules.operation_view.operation_view.model.OperationViewResp2.Item;
import cloud.demand.lab.modules.operation_view.operation_view.service.InstanceModelManualConfigService;
import cloud.demand.lab.modules.operation_view.operation_view.service.InventoryHealthGenService;
import cloud.demand.lab.modules.operation_view.operation_view.service.OperationViewService2;
import cloud.demand.lab.modules.operation_view.operation_view.web.OperationViewController2;
import cloud.demand.lab.modules.operation_view.operation_view_old.service.OperationViewService;
import cloud.demand.lab.modules.operation_view.operation_view_old.service.common.OperationViewCommonService;
import cloud.demand.lab.modules.operation_view.operation_view_old.utils.OperationViewTools;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.cache.HiSpeedCache;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.collect.MapUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import com.pugwoo.wooutils.redis.RedisHelper;
import com.pugwoo.wooutils.string.StringTools;
import io.vavr.Tuple2;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import javax.swing.plaf.ListUI;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.nutz.lang.Lang;
import org.nutz.lang.Strings;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;
import yunti.boot.config.DynamicProperty;
import yunti.boot.exception.BizException;

@Service
@Slf4j
public class OperationViewService2Impl implements OperationViewService2 {

    /**
     * 预测偏差率，其实这里的rainbow文案需要变更下，暂时不改
     * 1-预测偏差率 = 预测准确率
     */
    public static final Supplier<BigDecimal> SAFETY_INVENTORY_MAPE =
            DynamicProperty.create("safetyInventory.MAPE", "0.2",
                    BigDecimal::new);
//    /**
//     * 服务水平，默认0.95
//     */
//    public static final Supplier<BigDecimal> SAFETY_INVENTORY_SERVICE_LEVEL =
//            DynamicProperty.create("safetyInventory.serviceLevel", "0.95",
//                    BigDecimal::new);
//    /**
//     * 服务水平 - 弹性用量，默认0.95
//     */
//    public static final Supplier<BigDecimal> SAFETY_INVENTORY_SERVICE_LEVEL_FOR_BUFFER =
//            DynamicProperty.create("safetyInventory.serviceLevelForBuffer", "0.95",
//                    BigDecimal::new);

    private final ExecutorService threadPool = Executors.newFixedThreadPool(10);
    @Resource
    private OperationViewCommonService commonService;
    @Resource
    private DBHelper rrpDBHelper;
    @Resource
    private DictService baseDictService;
    @Resource
    private RedisHelper redisHelper;

    @Resource
    private DBHelper ckcldDBHelper;

    @Resource
    private OperationViewService operationViewService;
    @Resource
    private InventoryHealthGenService genService;
    @Resource
    private InventoryHealthDictService dictService;
    @Resource
    private DBHelper demandDBHelper;
    @Resource
    private DBHelper  ckcldStdCrpDBHelper;
    @Resource
    private TaskLogService taskLogService;

    @Resource
    private DBHelper ckcubesDBHelper;

    /**
     * 包年包月安全库存
     */
    public static BigDecimal calMonthlyInvNum(double serviceNum, BigDecimal sla, BigDecimal demandSD, BigDecimal demand, BigDecimal deliverySD) {
        BigDecimal variance = demandSD.pow(2);
        sla = AmountUtils.divideScale6(sla, BigDecimal.valueOf(7));

        BigDecimal demandVar = variance.multiply(sla);
        // 安全库存算法考虑交付周期
        if (demand != null && deliverySD != null) {
            deliverySD = AmountUtils.divideScale6(deliverySD, BigDecimal.valueOf(7));
            BigDecimal deliveryVar = demand.pow(2).multiply(deliverySD.pow(2));
            return BigDecimal.valueOf(Math.sqrt(demandVar.add(deliveryVar).doubleValue()) * serviceNum);
        }

        return BigDecimal.valueOf(Math.sqrt(demandVar.doubleValue()) * serviceNum);
    }

    @Override
    public OperationViewResp2 queryAllProductSummary(OperationViewReq2 req) {
        OperationViewResp2 resp = new OperationViewResp2();
        resp.setWeekInterval(getWeekInterval(DateUtils.format(req.getDate()), 13));

        List<OperationViewResp2.Item> data = Lang.list();
        resp.setData(data);

        //  剔除客户的逻辑
        if (ListUtils.isNotEmpty(req.getExcludeUinList())) {
            String statTime = DateUtils.formatDate(req.getDate());
            long count = ckcldDBHelper.getCount(DwsInventoryHealthWeeklyScaleDfDO.class,
                    "where stat_time = ? and exclude_uin_list = ?", statTime, req.handleExcludeUinList());
            if (count == 0) {
                genService.genSafetyInventoryDetail(statTime, req.getExcludeUinList(), Lang.list(), false);
            }
        }

        fillBaseFields(req, data); // 库存量要先计算，冗余库存依赖于其结果

//        CountDownLatch latch = new CountDownLatch(3);
        Future weekDiff = threadPool.submit(() -> {
            buildHistoryWeekDiff(req, resp);
            log.info("历史周需求done");
//            latch.countDown();
        });
        Future weekPeak = threadPool.submit(() -> {
            buildHistoryWeekPeak(req, resp);
            log.info("历史周净增done");
//            latch.countDown();
        });
//        Future futureWeekPeak = threadPool.submit(() -> {
//            buildFutureWeekPeak(req, resp);
//            log.info("未来周需求done");
////            latch.countDown();
//        });
        Future mckHistoryAndForecast = threadPool.submit(() -> {
            buildHistoryWeekPeakDemand(req, resp);
            log.info("mck历史和预估done");
        });

        //  设置超时5s，如果拿不到也返回，同时打印堆栈异常信息
        try {
//            latch.await(10, TimeUnit.SECONDS);
            weekDiff.get();
            weekPeak.get();
//            futureWeekPeak.get();
            mckHistoryAndForecast.get();
        } catch (ExecutionException | InterruptedException e) {
            log.error("接口queryAllProductSummary出现了异常, info:{}", e);
            throw BizException.makeThrow("接口queryAllProductSummary出现了异常, info:{}", e);
        }

        //  需要在上面这些方法执行结束后再统一收束处理
        //  补齐安全库存结果，不然会有安全库存为空，但弹性备货配额不为空的数据")
        //  此外对阈值进行设置，只要页面有那一行就会尝试去拿该维度下的阈值
        suppleNullData(resp, DateUtils.formatDate(req.getDate()));

        // 设置安全库存人工调整
        //  获取业务配置生效的算法
        EffectiveAlgorithmDTO effectiveAlgorithm
                = SpringUtil.getBean(OperationViewController2.class).getEffectiveAlgorithm();
        log.info("业务配置的生效算法为：" + effectiveAlgorithm.getAlgorithm());

        String algorithm = effectiveAlgorithm.getAlgorithm();

        for (OperationViewResp2.Item findOne : resp.getData()) {
            BigDecimal safeInvManualConfig = BigDecimal.ZERO;
            BigDecimal safeInv = null;
            BigDecimal turnoverInv = null;

            switch (algorithm) {
                case "historyWeekDiff":
                    if (findOne.getHistoryWeekDiff() == null) {
                        continue;
                    }

                    safeInvManualConfig = findOne.getHistoryWeekDiff().getSafeInvManualConfig();
                    safeInv = findOne.getHistoryWeekDiff().getSafetyInv();
                    turnoverInv = findOne.getHistoryWeekDiff().getTurnoverInv();
                    break;
                case "historyWeekPeak":
                    if (findOne.getHistoryWeekPeak() == null) {
//                        throw BizException.makeThrow("NULL " + findOne.getInstanceType() + findOne.getZoneName() + JSON.toJson(findOne));
                        continue;
                    }

                    safeInvManualConfig = findOne.getHistoryWeekPeak().getSafeInvManualConfig();
                    safeInv = findOne.getHistoryWeekPeak().getSafetyInv();
                    turnoverInv = findOne.getHistoryWeekPeak().getTurnoverInv();
                    break;
//                case "futureWeekPeak":
//                    if (findOne.getFutureWeekPeak() == null) {
//                        continue;
//                    }
//                    safeInvManualConfig = findOne.getFutureWeekPeak().getSafeInvManualConfig();
//                    safeInv = findOne.getFutureWeekPeak().getSafetyInv();
//                    break;
                case "historyWeekPeakForecastWN":
                    if (findOne.getHistoryWeekPeakForecastWN() == null) {
                        continue;
                    }
                    safeInvManualConfig = findOne.getHistoryWeekPeakForecastWN().getSafeInvManualConfig();
                    safeInv = findOne.getHistoryWeekPeakForecastWN().getSafetyInv();
                    turnoverInv = findOne.getHistoryWeekPeakForecastWN().getTurnoverInv();
                    break;
                default:
                    break;
            }

            findOne.setSafeInvManualConfig(safeInvManualConfig);

            if (turnoverInv == null) {
                turnoverInv = BigDecimal.ZERO;
            }
            if (safeInv == null) {
                safeInv = BigDecimal.ZERO;
            }
            BigDecimal allSafeInv = safeInv.add(turnoverInv);

            if (allSafeInv.compareTo(BigDecimal.ZERO) > 0) {
                findOne.setHealthRatio(NumberUtils.divide(findOne.getInvTotalNum(), allSafeInv, 2));
            } else {
                findOne.setHealthRatio(BigDecimal.ZERO);
            }

        }
        return resp;
    }

    private OperationViewExternalResp2 transformOpViewResp(OperationViewResp2 resp, LocalDate date, String effectiveAlgorithm) {
        // 业务要求简化提供给云产品的响应，从内层复用的响应结果中提取需要的内容
        OperationViewExternalResp2 opResp = new OperationViewExternalResp2();
        List<OperationViewExternalResp2.Item> opRespData = Lang.list();
        Map<String, BigDecimal> manualConfigMap =
                SpringUtil.getBean(ManualConfigServiceImpl.class).querySnapshotManual(date.toString());
        for (OperationViewResp2.Item item : resp.getData()) {
            OperationViewExternalResp2.Item opRespItem = new OperationViewExternalResp2.Item();
            opRespItem.setInstanceModel(""); // 暂时没这个粒度，但是业务希望后续支持，先留空
            opRespItem.setCustomhouseTitle(item.getCustomhouseTitle());
            opRespItem.setAreaName(item.getAreaName());
            opRespItem.setRegionName(item.getRegionName());
            opRespItem.setZoneName(item.getZoneName());
            opRespItem.setInstanceFamily(item.getInstanceType());
            opRespItem.setActualInv(item.getInvTotalNum());

            BigDecimal bufferSafetyInv = BigDecimal.ZERO;

            if (item.getBufferSafetyInv() != null) {
                bufferSafetyInv = item.getBufferSafetyInv();
            }
            log.info("可用区：" + opRespItem.getZoneName());
            log.info("弹性备货配额为：" + bufferSafetyInv);
            opRespItem.setBufferSafetyInv(bufferSafetyInv);

            if (effectiveAlgorithm.equals("historyWeekDiff")) {
                if (item.getHistoryWeekDiff() != null) {
                    opRespItem.setSafetyInv(item.getHistoryWeekDiff().getSafetyInv());
                    opRespItem.setMonthlySafetyInv(item.getHistoryWeekDiff().getMonthlySafetyInv());
                    opRespItem.setSafeInvManualConfig(item.getHistoryWeekDiff().getSafeInvManualConfig());
                }else {
                    opRespItem.setMonthlySafetyInv(BigDecimal.ZERO);
                    String key = String.join("@", item.getZoneName(), item.getInstanceType());
                    BigDecimal manualConfig = manualConfigMap.getOrDefault(key,BigDecimal.ZERO);
                    opRespItem.setSafeInvManualConfig(manualConfig);
                    opRespItem.setSafetyInv(NumberUtils.max(item.getBufferSafetyInv().add(manualConfig), BigDecimal.ZERO));
                }
            } else if (effectiveAlgorithm.equals("historyWeekPeak")) {
                if (item.getHistoryWeekPeak() != null) {
                    opRespItem.setSafetyInv(item.getHistoryWeekPeak().getSafetyInv());
                    opRespItem.setMonthlySafetyInv(item.getHistoryWeekPeak().getMonthlySafetyInv());
                    opRespItem.setSafeInvManualConfig(item.getHistoryWeekPeak().getSafeInvManualConfig());
                }else {
                    opRespItem.setMonthlySafetyInv(BigDecimal.ZERO);
                    String key = String.join("@", item.getZoneName(), item.getInstanceType());
                    BigDecimal manualConfig = manualConfigMap.getOrDefault(key,BigDecimal.ZERO);
                    opRespItem.setSafeInvManualConfig(manualConfig);
                    opRespItem.setSafetyInv(NumberUtils.max(item.getBufferSafetyInv().add(manualConfig), BigDecimal.ZERO));
                }
            } else if (effectiveAlgorithm.equals("futureWeekPeak")) {
                if (item.getFutureWeekPeak() != null) {
                    opRespItem.setSafetyInv(item.getFutureWeekPeak().getSafetyInv());
                    opRespItem.setMonthlySafetyInv(item.getFutureWeekPeak().getMonthlySafetyInv());
                    opRespItem.setSafeInvManualConfig(item.getFutureWeekPeak().getSafeInvManualConfig());
                }else {
                    opRespItem.setMonthlySafetyInv(BigDecimal.ZERO);
                    String key = String.join("@", item.getZoneName(), item.getInstanceType());
                    BigDecimal manualConfig = manualConfigMap.getOrDefault(key,BigDecimal.ZERO);
                    opRespItem.setSafeInvManualConfig(manualConfig);
                    opRespItem.setSafetyInv(NumberUtils.max(item.getBufferSafetyInv().add(manualConfig), BigDecimal.ZERO));
                }
            }else if (effectiveAlgorithm.equals("historyWeekPeakForecastWN")) {
                if (item.getHistoryWeekPeakForecastWN() != null) {
                    opRespItem.setSafetyInv(item.getHistoryWeekPeakForecastWN().getSafetyInv());
                    opRespItem.setMonthlySafetyInv(item.getHistoryWeekPeakForecastWN().getMonthlySafetyInv());
                    opRespItem.setSafeInvManualConfig(item.getHistoryWeekPeakForecastWN().getSafeInvManualConfig());
                }else {
                    opRespItem.setMonthlySafetyInv(BigDecimal.ZERO);
                    String key = String.join("@", item.getZoneName(), item.getInstanceType());
                    BigDecimal manualConfig = manualConfigMap.getOrDefault(key,BigDecimal.ZERO);
                    opRespItem.setSafeInvManualConfig(manualConfig);
                    opRespItem.setSafetyInv(NumberUtils.max(item.getBufferSafetyInv().add(manualConfig), BigDecimal.ZERO));
                }
            }

            // 实际库存冗余系数 = 实际库存 / 安全库存
            if (opRespItem.getSafetyInv() != null && opRespItem.getSafetyInv().compareTo(BigDecimal.ZERO) > 0) {
                opRespItem.setRedundantInvRatio(opRespItem.getActualInv().divide(opRespItem.getSafetyInv(), 2, RoundingMode.HALF_UP));
            }

            // 目标冗余系数，从七彩石获取
            Double targetRedundantInvRatio = DynamicProperties.targetRedundantInvRatio.get().get("" + date.getYear());
            opRespItem.setTargetRedundantInvRatio(BigDecimal.valueOf(targetRedundantInvRatio));
            log.info("目标冗余系数：年份" + date.getYear() + targetRedundantInvRatio + "" + DynamicProperties.targetRedundantInvRatio.get());
            // 实际冗余量 = max(实际库存量-冗余系数目标值*安全库存量,0)
            if (opRespItem.getSafetyInv() != null && opRespItem.getActualInv() != null) {
                BigDecimal redundantEnv = opRespItem.getActualInv().subtract(opRespItem.getTargetRedundantInvRatio().multiply(opRespItem.getSafetyInv()));
                opRespItem.setRedundantInv(redundantEnv);
//                if (redundantEnv.compareTo(BigDecimal.ZERO) > 0) {
//                    opRespItem.setRedundantInv(redundantEnv);
//                } else {
//                    opRespItem.setRedundantInv(BigDecimal.ZERO);
//                }
            }

            opRespData.add(opRespItem);
        }

        opResp.setData(opRespData);

        return opResp;
    }

    @Override
    public OperationViewExternalResp2 queryAllProductSummaryExternal(OperationViewExternalReq2 req) {
        OperationViewResp2 resp = new OperationViewResp2();

        List<OperationViewResp2.Item> data = Lang.list();
        resp.setData(data);

        // 因业务要求，外侧简化了提供给云产品的参数，内层为复用代码，按业务要求条件构建原参数
        OperationViewReq2 opReq2 = new OperationViewReq2();
        log.info("查询日期 " + req.getDate() + " 的安全库存数据");
        opReq2.setDate(req.getDate());

        // 查询范围：主力园区、辅助园区的主力机型+在售非主力机型对应的数据
        opReq2.setZoneCategory(ListUtils.newList("PRINCIPAL", "SECONDARY"));
        opReq2.setInstanceTypeCategory(ListUtils.newList("PRINCIPAL", "ON_SALE"));
        // 只包含好料
        opReq2.setMaterialType(ListUtils.newList(("好料")));

        //  三种算法的公共部分填充
        fillBaseFields(opReq2, data);

        //  获取业务配置生效的算法
        EffectiveAlgorithmDTO effectiveAlgorithm
                = SpringUtil.getBean(OperationViewController2.class).getEffectiveAlgorithm();
        log.info("业务配置的生效算法为：" + effectiveAlgorithm.getAlgorithm());
        if (effectiveAlgorithm == null || StringTools.isBlank(effectiveAlgorithm.getAlgorithm())) {
            return transformOpViewResp(resp, req.getDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate(), effectiveAlgorithm.getAlgorithm());
        }

        String algorithm = effectiveAlgorithm.getAlgorithm();

        switch (algorithm) {
            case "historyWeekDiff":
                buildHistoryWeekDiff(opReq2, resp);
                break;
            case "historyWeekPeak":
                buildHistoryWeekPeak(opReq2, resp);
                break;
            case "futureWeekPeak":
                buildFutureWeekPeak(opReq2, resp);
                break;
            case "historyWeekPeakForecastWN":
                buildHistoryWeekPeakDemand(opReq2, resp);
                break;
            default:
                break;
        }

        return transformOpViewResp(resp, req.getDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate(), effectiveAlgorithm.getAlgorithm());
    }

    public Set<String> getInstanceTypesByInstanceCategory(List<String> instanceTypeCategory, String date, List<String> customhouseTitle) {
        Map<String, List<String>> instanceTypeMap = getInstanceTypeConfigMap(date, customhouseTitle);
        Set<String> allInstanceTypes = new HashSet<>();

        if (ListUtils.isNotEmpty(instanceTypeCategory)) {
            allInstanceTypes = instanceTypeCategory.stream().flatMap(type -> instanceTypeMap.getOrDefault(type, ListUtils.newList()).stream()).collect(
                    Collectors.toSet());
        } else {
            for (String instanceType : instanceTypeMap.keySet()) {
                allInstanceTypes.addAll(instanceTypeMap.getOrDefault(instanceType, ListUtils.newList()));
            }
        }

        return allInstanceTypes;
    }

    public List<String> getZoneNamesByZoneCategory(List<String> zoneCategory, String date) {
        Map<String, List<String>> zoneTypeMap = getZoneConfigMapContainCustomhouseTitle(date);
        List<String> allZoneNames = new ArrayList<>();

        if (ListUtils.isNotEmpty(zoneCategory)) {
            allZoneNames = zoneCategory.stream().flatMap(type -> zoneTypeMap.getOrDefault(type, ListUtils.newList()).stream()).collect(Collectors.toList());
        } else {
            for (String zoneName : zoneTypeMap.keySet()) {
                allZoneNames.addAll(zoneTypeMap.getOrDefault(zoneName, ListUtils.newList()));
            }
        }


        return allZoneNames;
    }

    public Map<String, List<String>> getInstanceTypeConfigMap(String today, List<String> customhouseTitle) {
        QueryInstanceTypeConfigReq req = new QueryInstanceTypeConfigReq();
        req.setDate(today);
        List<String> temp = new ArrayList<>();
        if (ListUtils.isNotEmpty(customhouseTitle)) {
            temp.addAll(customhouseTitle);
            if (temp.contains("境外")) {
                temp.add("海外");
            }
        }
        req.setCustomhouseTitle(temp);
        WhereSQL cond = req.genCondition();
        List<InventoryHealthMainInstanceTypeConfigDO> result = demandDBHelper.getAll(InventoryHealthMainInstanceTypeConfigDO.class, cond.getSQL(), cond.getParams());
        for (InventoryHealthMainInstanceTypeConfigDO item : result) {
            if (item.getRegionType().equals("海外")) {
                item.setRegionType("境外");
            }
        }
        // 排序按照境内外（境内、境外）、一级类型（主力机型、其他状态、EOL机型）
        List<String> regionTypes = ListUtils.newList("境内", "境外");
        List<String> types = ListUtils.newList("主力机型", "其他机型", "EOL机型");
        List<String> types2 = ListUtils.newList("主力机型", "在售非主力机型", "新机型", "EOL停供机型", "EOL库存机型");

        result.sort((o1, o2) -> {
            Integer o1RegionTypeIndex = regionTypes.indexOf(o1.getRegionType());
            Integer o2RegionTypeIndex = regionTypes.indexOf(o2.getRegionType());

            if (o1RegionTypeIndex == o2RegionTypeIndex) {
                Integer o1TypeIndex = types.indexOf(o1.getType1Name());
                Integer o2TypeIndex = types.indexOf(o2.getType1Name());

                if (o1TypeIndex == o2TypeIndex) {
                    Integer o1Type2Index = types2.indexOf(o1.getType2Name());
                    Integer o2Type2Index = types2.indexOf(o2.getType2Name());

                    return o1Type2Index.compareTo(o2Type2Index);
                }

                return o1TypeIndex.compareTo(o2TypeIndex);
            }

            return o1RegionTypeIndex.compareTo(o2RegionTypeIndex);
        });

        if (result.isEmpty()) {
            result = demandDBHelper.getRaw(InventoryHealthMainInstanceTypeConfigDO.class, "select * from inventory_health_main_instance_type_config where date = (select min(`date`) from inventory_health_main_instance_type_config where deleted = 0) and deleted = 0");
        }

        return ListUtils.toMapList(result, InventoryHealthMainInstanceTypeConfigDO::getType2, InventoryHealthMainInstanceTypeConfigDO::getInstanceType);
    }

    public Map<String, List<String>> getZoneConfigMapContainCustomhouseTitle(String today) {
        QueryZoneConfigReq req = new QueryZoneConfigReq();
        req.setDate(today);
        WhereSQL cond = req.genCondition();
        List<InventoryHealthMainZoneNameConfigDO> result = demandDBHelper.getAll(InventoryHealthMainZoneNameConfigDO.class, cond.getSQL(), cond.getParams());

        // 排序按照境内外（境内、境外）、类型（主力园区、辅助园区、待收敛园区、收敛园区、其他园区）
        List<String> regionTypes = ListUtils.newList("境内", "境外");
        List<String> types = ListUtils.newList("主力可用区", "辅助可用区", "待收敛可用区", "已收敛可用区", "特殊专区", "其他可用区");

        result.sort((o1, o2) -> {
            Integer o1RegionTypeIndex = regionTypes.indexOf(o1.getRegionType());
            Integer o2RegionTypeIndex = regionTypes.indexOf(o2.getRegionType());

            if (o1RegionTypeIndex == o2RegionTypeIndex) {
                Integer o1TypeIndex = types.indexOf(o1.getTypeName());
                Integer o2TypeIndex = types.indexOf(o2.getTypeName());

                return o1TypeIndex.compareTo(o2TypeIndex);
            }

            return o1RegionTypeIndex.compareTo(o2RegionTypeIndex);
        });
        if (result.isEmpty()) {
            result = demandDBHelper.getRaw(InventoryHealthMainZoneNameConfigDO.class, "select * from inventory_health_main_zone_name_config where date = (select min(`date`) from inventory_health_main_zone_name_config where deleted = 0) and deleted = 0");
        }
        return ListUtils.toMapList(result, InventoryHealthMainZoneNameConfigDO::getType, InventoryHealthMainZoneNameConfigDO::getZoneName);
    }

    /**
     * 生成园区分类和机型分类的Where条件
     * 2023-07-17:添加组合机型情况下的优先级过滤
     * 如果组合机型，选中主力机型后，返回机型组合中所有包含主力机型的组合机型列表数据
     * 如：SA2（主力）/MA2（非主力），选中组合机型后也返回MA2的机型数据
     */
    public WhereSQL genCategoryCondition(List<String> zoneCategory, List<String> instanceTypeCategory, Boolean isCombine, String date, List<String> customhouseTitle) {
        WhereSQL condition = new WhereSQL();
        if (ListUtils.isEmpty(zoneCategory) && ListUtils.isEmpty(instanceTypeCategory)) {
            return condition;
        }
        WhereSQL zoneCondition = new WhereSQL();
        WhereSQL instanceTypeCondition = new WhereSQL();

        boolean containsUndefinedZoneCategory = false;

        if (ListUtils.isNotEmpty(zoneCategory)) {
            containsUndefinedZoneCategory = zoneCategory.stream().anyMatch(t -> t.equals("undefined"));
        }
        List<String> negativeAllZoneNames = null;
        if (containsUndefinedZoneCategory) {
            zoneCategory = zoneCategory.stream().filter(t -> !t.equals("undefined")).collect(Collectors.toList());
            negativeAllZoneNames = getZoneNamesByZoneCategory(ListUtils.newList(), date);
        }
        List<String> allZoneNames = getZoneNamesByZoneCategory(zoneCategory, date);


        if (ListUtils.isNotEmpty(zoneCategory)) {
            zoneCondition.or("zone_name in (?)", allZoneNames);
        }
        if (ListUtils.isNotEmpty(negativeAllZoneNames)) {
            zoneCondition.or("zone_name not in (?)", negativeAllZoneNames);
        }


        boolean containsUndefinedCategory = false;

        if (ListUtils.isNotEmpty(instanceTypeCategory)) {
            containsUndefinedCategory = instanceTypeCategory.stream().anyMatch(t -> t.equals("undefined"));
        }

        Set<String> negativeAllInstanceTypes = null;

        if (containsUndefinedCategory) {
            instanceTypeCategory = instanceTypeCategory.stream().filter(t -> !t.equals("undefined")).collect(Collectors.toList());
            negativeAllInstanceTypes = getInstanceTypesByInstanceCategory(ListUtils.newList(), date, customhouseTitle);
        }

        Set<String> allInstanceTypes = getInstanceTypesByInstanceCategory(instanceTypeCategory, date, customhouseTitle);

        //  组合机型逻辑
        List<Set<String>> combinations = dictService.queryAllCombineInstanceType().getCombination();

        if (isCombine != null && isCombine) {
            //  遍历每组组合机型数据
            for (Set<String> combination : combinations) {
                for (String each : combination) {
                    if (allInstanceTypes.contains(each)) {
                        allInstanceTypes.addAll(combination);
                        break;
                    }
                }
            }
        }

        if (ListUtils.isNotEmpty(instanceTypeCategory)) {
            instanceTypeCondition.or("instance_type in (?)", allInstanceTypes);
        }

        if (negativeAllInstanceTypes != null) {
            instanceTypeCondition.or("instance_type not in (?)", negativeAllInstanceTypes);
        }

        condition.and(zoneCondition);
        condition.and(instanceTypeCondition);
        return condition;
    }

    public WhereSQL genBigDataCategoryCondition(List<String> zoneCategory, List<String> instanceTypeCategory, Boolean isCombine, String date, List<String> customhouseTitle) {
        WhereSQL condition = new WhereSQL();
        if (ListUtils.isEmpty(zoneCategory) && ListUtils.isEmpty(instanceTypeCategory)) {
            return condition;
        }

        List<String> allZoneNames = getZoneNamesByZoneCategory(zoneCategory, date);
        WhereSQL zoneCondition = new WhereSQL();
        WhereSQL instanceTypeCondition = new WhereSQL();

        if (ListUtils.isNotEmpty(zoneCategory)) {
            zoneCondition.or("zone_name in (?)", allZoneNames);
        }

        boolean containsUndefinedCategory = false;

        if (ListUtils.isNotEmpty(instanceTypeCategory)) {
            containsUndefinedCategory = instanceTypeCategory.stream().anyMatch(t -> t.equals("undefined"));
        }

        Set<String> negativeAllInstanceTypes = null;

        if (containsUndefinedCategory) {
            instanceTypeCategory = instanceTypeCategory.stream().filter(t -> !t.equals("undefined")).collect(Collectors.toList());
            negativeAllInstanceTypes = getInstanceTypesByInstanceCategory(ListUtils.newList(), date, customhouseTitle);
        }

        Set<String> allInstanceTypes = getInstanceTypesByInstanceCategory(instanceTypeCategory, date, customhouseTitle);

        //  组合机型逻辑
        List<Set<String>> combinations = dictService.queryAllCombineInstanceType().getCombination();

        if (isCombine != null && isCombine) {
            //  遍历每组组合机型数据
            for (Set<String> combination : combinations) {
                for (String each : combination) {
                    if (allInstanceTypes.contains(each)) {
                        allInstanceTypes.addAll(combination);
                        break;
                    }
                }
            }
        }

        if (ListUtils.isNotEmpty(instanceTypeCategory)) {
            instanceTypeCondition.or("instance_family in (?)", allInstanceTypes);
        }

        if (negativeAllInstanceTypes != null) {
            instanceTypeCondition.or("instance_family not in (?)", negativeAllInstanceTypes);
        }

        condition.and(zoneCondition);
        condition.and(instanceTypeCondition);
        return condition;
    }
    @Override
    @HiSpeedCache(expireSecond = 600, continueFetchSecond = 3600, keyScript = "args[0]",
            useRedis = true, cacheRedisDataMillisecond = 60000)
    public List<InventoryHealthZlkhbSafetyInventorySnapshotDO> queryHeadZlkhbData(String statTime) {
        return demandDBHelper.getAll(InventoryHealthZlkhbSafetyInventorySnapshotDO.class,
                "where stat_time = ?", statTime);
    }

    public void fillNullFutureWeekPeakSafetyInvItem(OperationViewResp2.Item item, String statTime) {
        OperationViewResp2.SafetyInventoryResult result = buildSafetyInventoryResult(item, statTime);

        item.setFutureWeekPeak(result);
        // 安全库存人工调整
        Map<String, BigDecimal> manualConfigMap =
                SpringUtil.getBean(ManualConfigServiceImpl.class).querySnapshotManual(statTime);
        String manualConfigKey = Strings.join("@", item.getZoneName(), item.getInstanceType());

        result.setSafeInvManualConfig(manualConfigMap.getOrDefault(manualConfigKey, BigDecimal.ZERO));

        if (result.getSafetyInv() != null) {
            result.setSafetyInv(NumberUtils.max(result.getSafetyInv().add(result.getSafeInvManualConfig()), BigDecimal.ZERO));
        }

        result.setAlgorithm("未来周需求");
        result.setForecastDemandCore(BigDecimal.ZERO);
        result.setForecastDemandAccuracyRate(SAFETY_INVENTORY_MAPE.get());
    }

    public void fillNullHistoryWeekPeakSafetyInvItem(OperationViewResp2.Item item, String statTime) {
        OperationViewResp2.SafetyInventoryResult result = buildSafetyInventoryResult(item, statTime);
        item.setHistoryWeekPeak(result);

        // 安全库存人工调整
        Map<String, BigDecimal> manualConfigMap =
                SpringUtil.getBean(ManualConfigServiceImpl.class).querySnapshotManual(statTime);

        String manualConfigKey = Strings.join("@", item.getZoneName(), item.getInstanceType());

        result.setSafeInvManualConfig(manualConfigMap.getOrDefault(manualConfigKey, BigDecimal.ZERO));

        if (result.getSafetyInv() != null) {
            result.setSafetyInv(NumberUtils.max(result.getSafetyInv().add(result.getSafeInvManualConfig()), BigDecimal.ZERO));
        }

        result.setAlgorithm("历史周峰");
        result.setWeekDemandMap(new HashMap<>());
    }

    public void fillNullHistoryWeekPeakForecastWeekNSafetyInvItem(OperationViewResp2.Item item, String statTime) {
        OperationViewResp2.SafetyInventoryResult result = buildSafetyInventoryResult(item, statTime);
        item.setHistoryWeekPeakForecastWN(result);

        // 安全库存人工调整
        Map<String, BigDecimal> manualConfigMap =
                SpringUtil.getBean(ManualConfigServiceImpl.class).querySnapshotManual(statTime);

        String manualConfigKey = Strings.join("@", item.getZoneName(), item.getInstanceType());

        result.setSafeInvManualConfig(manualConfigMap.getOrDefault(manualConfigKey, BigDecimal.ZERO));

        if (result.getSafetyInv() != null) {
            result.setSafetyInv(NumberUtils.max(result.getSafetyInv().add(result.getSafeInvManualConfig()), BigDecimal.ZERO));
        }
        // 周转库存默认设置成 null
        result.setTurnoverInv(null);
        result.setAlgorithm("MCK历史&预测需求");
        result.setWeekDemandMap(new HashMap<>());
    }

    public void fillNullHistoryWeekDiffSafetyInvItem(OperationViewResp2.Item item, String statTime) {
        OperationViewResp2.SafetyInventoryResult result = buildSafetyInventoryResult(item, statTime);
        item.setHistoryWeekDiff(result);

        // 安全库存人工调整
        Map<String, BigDecimal> manualConfigMap =
                SpringUtil.getBean(ManualConfigServiceImpl.class).querySnapshotManual(statTime);
        String manualConfigKey = Strings.join("@", item.getZoneName(), item.getInstanceType());

        result.setSafeInvManualConfig(manualConfigMap.getOrDefault(manualConfigKey, BigDecimal.ZERO));

        if (result.getSafetyInv() != null) {
            result.setSafetyInv(NumberUtils.max(result.getSafetyInv().add(result.getSafeInvManualConfig()), BigDecimal.ZERO));
        }

        result.setAlgorithm("历史周需求");
        result.setWeekDemandMap(new HashMap<>());
    }

    public void suppleNullData(OperationViewResp2 resp, String statTime) {
        List<OperationViewResp2.Item> data = resp.getData();

        //  实时阈值
        Map<String, BigDecimal> realTimeThresholdMap =
                SpringUtil.getBean(ThresholdTransferServiceImpl.class).queryRealTimeThreshold();
        //  切片阈值
        Map<String, BigDecimal> snapshotThresholdMap =
                SpringUtil.getBean(ThresholdTransferServiceImpl.class).querySnapshotThreshold(statTime);

        for (OperationViewResp2.Item item : data) {
            item.setSafetyInvThreshold(
                    realTimeThresholdMap.get(Strings.join("@", item.getZoneName(), item.getInstanceType())));
            item.setSafetyInvThresholdSnapshot(
                    snapshotThresholdMap.get(Strings.join("@", item.getZoneName(), item.getInstanceType())));

            item.setBufferSafetyInv(item.getBufferSafetyInv() == null ? BigDecimal.ZERO : item.getBufferSafetyInv());
            if (item.getFutureWeekPeak() == null) {
                fillNullFutureWeekPeakSafetyInvItem(item, statTime);
            }
            if (item.getHistoryWeekDiff() == null) {
                fillNullHistoryWeekDiffSafetyInvItem(item, statTime);
            }
            if (item.getHistoryWeekPeak() == null) {
                fillNullHistoryWeekPeakSafetyInvItem(item, statTime);
            }
            if (item.getHistoryWeekPeakForecastWN() == null) {
                fillNullHistoryWeekPeakForecastWeekNSafetyInvItem(item, statTime);
            }
        }
    }

    public BigDecimal getTargetServiceLevel(String date, String zoneName, String instanceType, String billType) {
        InventoryHealthConfigService inventoryHealthConfigService = SpringUtil.getBean(InventoryHealthConfigServiceImpl.class);
        Map<String, BigDecimal> resultMap = inventoryHealthConfigService.queryTargetServiceLevel(date);
        Map<String, InventoryHealthMainZoneNameConfigDO> zoneNameTypeMap = inventoryHealthConfigService.getZoneNameToTypeConfigMap(date);
        Map<String, String> instanceTypeType2map = inventoryHealthConfigService.getInstanceTypeToType2ConfigMap(date);

        InventoryHealthMainZoneNameConfigDO zoneInfo = zoneNameTypeMap.get(zoneName);
        String zoneType = null;
        String regionType = null;

        if (zoneInfo != null) {
            zoneType = zoneInfo.getType();
            regionType = zoneInfo.getRegionType();
        }

        String instanceTypeType2 = instanceTypeType2map.get(instanceType);

        // 找不到则告警
        if (zoneType == null || regionType == null || instanceTypeType2 == null) {
//            this.taskLogService.genRunLog("getTargetServiceLevel", "getTargetServiceLevel", "库存健康配置里面找不到类型配置。" + zoneName + " " + instanceType + " " + zoneType + " " + regionType + " " + instanceTypeType2);
            return BigDecimal.ZERO;
        }

        String key = InventoryHealthConfigServiceImpl.getTargetServiceLevelKey(regionType, zoneType, instanceTypeType2, billType);
        BigDecimal result = resultMap.get(key);

        if (result == null) {
            this.taskLogService.genRunLog("getTargetServiceLevel", "getTargetServiceLevel", "库存健康配置里面找不到指定的服务水平。" + key);
            return BigDecimal.ZERO;
        }

        return result;
    }

    public BigDecimal getTargetServiceLevel(
            Map<String, BigDecimal> resultMap,
            Map<String, InventoryHealthMainZoneNameConfigDO> zoneNameTypeMap,
            Map<String, String> instanceTypeType2map ,
            String zoneName, String instanceType, String billType) {
        InventoryHealthMainZoneNameConfigDO zoneInfo = zoneNameTypeMap.get(zoneName);
        String zoneType = null;
        String regionType = null;

        if (zoneInfo != null) {
            zoneType = zoneInfo.getType();
            regionType = zoneInfo.getRegionType();
        }

        String instanceTypeType2 = instanceTypeType2map.get(instanceType);

        // 找不到则告警
        if (zoneType == null || regionType == null || instanceTypeType2 == null) {
//            this.taskLogService.genRunLog("getTargetServiceLevel", "getTargetServiceLevel", "库存健康配置里面找不到类型配置。" + zoneName + " " + instanceType + " " + zoneType + " " + regionType + " " + instanceTypeType2);
            return BigDecimal.ZERO;
        }

        String key = InventoryHealthConfigServiceImpl.getTargetServiceLevelKey(regionType, zoneType, instanceTypeType2, billType);
        BigDecimal result = resultMap.get(key);

        if (result == null) {
            this.taskLogService.genRunLog("getTargetServiceLevel", "getTargetServiceLevel", "库存健康配置里面找不到指定的服务水平。" + key);
            return BigDecimal.ZERO;
        }

        return result;
    }

    /**
     * 当三种算法的安全库存结果都是空但弹性备货配额结果不为空时构造对象
     */
    private OperationViewResp2.SafetyInventoryResult buildSafetyInventoryResult(OperationViewResp2.Item item, String date) {
        OperationViewResp2.SafetyInventoryResult result = new OperationViewResp2.SafetyInventoryResult();
        result.setSafetyInv(item.getBufferSafetyInv());
        result.setMonthlySafetyInv(BigDecimal.ZERO);
        result.setSla(operationViewService.getSLAByInstanceType(item.getInstanceType(),
                !"境外".equals(item.getCustomhouseTitle())));
        result.setBufferSafetyInv(item.getBufferSafetyInv());
        result.setTurnoverInv(BigDecimal.ZERO);
        result.setRedundancyInv(AmountUtils.subtract(result.getSafetyInv(), result.getTurnoverInv()));
        result.setStandardDiff(BigDecimal.ZERO);
        result.setDemandAvg(BigDecimal.ZERO);
        BigDecimal serviceLevel = this.getTargetServiceLevel(date, item.getZoneName(), item.getInstanceType(), "包年包月");
        result.setServiceLevel(serviceLevel);
        result.setServiceLevelFactor(
                BigDecimal.valueOf(OperationViewTools.normsinv(serviceLevel.doubleValue())));

        return result;
    }

    public List<String> getIndicatorCodesFromLineType(List<String> lineType) {
        List<String> indicatorCodeForLineType = new ArrayList<>(); // 库存类型

        if (ListUtils.isEmpty(lineType)) { // 全部都要
            indicatorCodeForLineType.add("d1");
            indicatorCodeForLineType.add("d2");
            indicatorCodeForLineType.add("d3");
            indicatorCodeForLineType.add("e4");
            indicatorCodeForLineType.add("e8");
        } else {
            if (lineType.contains("线上库存")) {
                indicatorCodeForLineType.add("d1");
                indicatorCodeForLineType.add("d2");
                indicatorCodeForLineType.add("d3");
            }
            if (lineType.contains("线下搬迁")) {
                indicatorCodeForLineType.add("e8");
            }
            if (lineType.contains("线下流转")) {
                indicatorCodeForLineType.add("e4");
            }
        }

        return indicatorCodeForLineType;
    }

    /**
     * @Deprecated 现在实际库存数据源已经切换到腾讯云 CVM 库存取数，这一段共用的筛选逻辑废弃了
     */
//    public void genBaseFieldsCondition(WhereSQL condition, List<String> lineType, List<String> zoneCategory, List<String> instanceTypeCategory, Boolean isCombine, boolean isGroupByStatTime, String date) {
//        condition.and("product_type = 'CVM' and compute_type = 'CPU'");
//        List<String> indicatorCodeForLineType = getIndicatorCodesFromLineType(lineType);
//
//        //  针对机型类型、园区类型进行筛选
//        WhereSQL categoryCondition = genCategoryCondition(zoneCategory, instanceTypeCategory, isCombine, date);
//        condition.and(categoryCondition);
//
//        condition.and("indicator_code in (?)", indicatorCodeForLineType);
//
//        if (isGroupByStatTime) {
//            condition.addGroupBy("zone_name", "customhouse_title", "area_name",
//                    "region_name", "instance_type", "device_type", "material_type", "stat_time");
//        } else {
//            condition.addGroupBy("zone_name", "customhouse_title", "area_name",
//                    "region_name", "instance_type", "device_type", "material_type");
//        }
//    }

    public List<DwsActualInventoryDfDO> getActualInventoryList(ActualInventoryListReq params, WhereSQL extraCondition) {
        WhereSQL condition = new WhereSQL();
        condition.and(extraCondition);
        condition.and("stat_time = ?", params.getStatTime());

        if (ListUtils.isNotEmpty(params.getLineType())) {
            condition.and("line_type in (?)", params.getLineType());
        }

        if (ListUtils.isNotEmpty(params.getMaterialType())) {
            condition.and("material_type in (?)", params.getMaterialType());
        }

        String categoryDate = params.getCategoryDate();

        if (StringTools.isBlank(categoryDate)) {
            categoryDate = params.getStatTime();
        }

        WhereSQL categoryCondition = genCategoryCondition(params.getZoneCategory(), params.getInstanceTypeCategory(), params.getIsCombine(), categoryDate, params.getCustomhouseTitle());
        condition.and(categoryCondition);

        if (ListUtils.isNotEmpty(params.getInvDetailType())) {
            List<String> invDetailType = ListUtils.newList();
            invDetailType.addAll(params.getInvDetailType());
            // 历史数据(2024-01-11之前)，类型始终为空值，需要将空值加上
            if (!invDetailType.contains("(空值)")) {
                invDetailType.add("(空值)");
            }

            condition.and("inv_detail_type in (?)", invDetailType);
        }

        return ckcldDBHelper.getAll(DwsActualInventoryDfDO.class, condition.getSQL(), condition.getParams()).stream().map(item -> {
            item.setDate(item.getStatTime());
            return item;
        }).collect(Collectors.toList());
    }


    /**
     *
     * @param params 复用实际库存的参数，不过有一些字段比如 lineType，materialType 不会被用到
     * @param extraCondition
     * @return
     */
    public List<DwsBufferSafeInventoryDfDO> getBufferSafeInventoryList(ActualInventoryListReq params, WhereSQL extraCondition) {
        WhereSQL condition = new WhereSQL();
        condition.and(extraCondition);
        condition.and("stat_time = ?", params.getStatTime());
        condition.and("product_type = 'CVM'");

        String categoryDate = params.getCategoryDate();

        if (StringTools.isBlank(categoryDate)) {
            categoryDate = params.getStatTime();
        }

        WhereSQL categoryCondition = genCategoryCondition(params.getZoneCategory(), params.getInstanceTypeCategory(), params.getIsCombine(), categoryDate, params.getCustomhouseTitle());
        condition.and(categoryCondition);

        return ckcldDBHelper.getAll(DwsBufferSafeInventoryDfDO.class, condition.getSQL(), condition.getParams()).stream().map(item -> {
            // 确保弹性备货 > 0
            item.setMckBufferSafetyInv(NumberUtils.max(item.getMckBufferSafetyInv(), BigDecimal.ZERO));
            item.setBufferSafetyInv(NumberUtils.max(item.getBufferSafetyInv(), BigDecimal.ZERO));
            return item;
        }).collect(Collectors.toList());
    }

//    public List<ActualInventoryAvgDTO> filterByMaterialType(List<ActualInventoryAvgDTO> all, List<String> materialType) {
//        Map<String, StaticStockPrincipalHosttypeDO> allGoodDeviceType = baseDictService.getAllGoodDeviceType();
//        ListUtils.forEach(ListUtils.filter(all, o -> StringTools.isBlank(o.getMaterialType())),
//                o -> o.setMaterialType(allGoodDeviceType.containsKey(o.getDeviceType()) ? "好料" : "差料"));
//
//        //  过滤好差呆
//        return ListUtils.filter(all, o -> {
//            if (ListUtils.isEmpty(materialType)) { // 为空则不限定，都要
//                return true;
//            }
//            return materialType.contains(o.getMaterialType());
//        });
//    }

    /**
     * 生成基准的库存相关数据
     */
    public void fillBaseFields(OperationViewReq2 req, List<OperationViewResp2.Item> data) {
        //  1、获取数据
        String statTime = DateUtils.formatDate(req.getDate());
        WhereSQL condition = req.genCondition();

        ActualInventoryListReq params = new ActualInventoryListReq();
        params.setStatTime(statTime);
        params.setLineType(req.getLineType());
        params.setZoneCategory(req.getZoneCategory());
        params.setIsCombine(req.getIsCombine());
        params.setInvDetailType(req.getInvDetailType());
        params.setInstanceTypeCategory(req.getInstanceTypeCategory());
        params.setMaterialType(req.getMaterialType());
        params.setCustomhouseTitle(req.getCustomhouseTitle());
        List<DwsActualInventoryDfDO> all = getActualInventoryList(params, condition);
        // 填充通用的条件
//        genBaseFieldsCondition(condition, req.getLineType(), req.getZoneCategory(), req.getInstanceTypeCategory(), req.getIsCombine(), true, DateUtils.formatDate(req.getDate()));
//
//        List<ActualInventoryDTO> all =
//                ckcldDBHelper.getAll(ActualInventoryDTO.class, condition.getSQL(), condition.getParams());

        //  2、 过滤好差呆类型
        //  补全线下库存部分的好差类型的字段
//        all = filterByMaterialType(all.stream().map(item -> (ActualInventoryAvgDTO) item).collect(Collectors.toList()), req.getMaterialType()).stream().map(item -> (ActualInventoryDTO) item).collect(Collectors.toList());

//        // 3、找到最近1个月的弹性用量数据，计算出弹性用来的安全库存 X%(服务水平）*L(弹性量/天)
//        //    L=“近”一个月的弹性用量“天”均数据
//        List<BufferAverageCoreDTO> bufferAverageCoreDTOS =
//                SpringUtil.getBean(JxcExternalServiceImpl.class).queryBufferCoreAverage(DateUtils.parse(statTime));
//        //  对弹性规模在内存中筛选
//        if (condition != null) {
//            bufferAverageCoreDTOS = filterBufferPart(bufferAverageCoreDTOS, req, DateUtils.formatDate(req.getDate()));
//        }

        LocalDate date = LocalDate.parse(statTime);
        String curWeekMonday = DateUtils.formatDate(date.with(DayOfWeek.MONDAY));
        params.setCategoryDate(statTime);
        params.setStatTime(curWeekMonday);
        List<DwsBufferSafeInventoryDfDO> bufferSafeInventoryDfDOS = getBufferSafeInventoryList(params, condition);
        //  4、合并实际库存和弹性备货配额的结果，传给下游
//        all = mergeData(
//                statTime,
//                all.stream().map(item -> (ActualInventoryAvgDTO) item).collect(Collectors.toList()),
//                bufferAverageCoreDTOS
//        ).stream().map(item -> (ActualInventoryDTO) item).collect(Collectors.toList());

        //  5、数据加工
        // 获取真实服务水平
        Map<String, SlaResultItem> slaMap = getSlaMap(statTime);
        //  该产品当天的总容量
        BigDecimal curDayTotalCapacity = getCurDayTotalCapacity(statTime);
        //  产品单价表，只要CVM产品的
        Map<String, BigDecimal> unitPriceMap = baseDictService.getProductUnitPrice();
        BigDecimal unitPrice = unitPriceMap.get("CVM");

        // 合并实际库存和弹性库存的结果，并汇总到总的结果中
        data.addAll(mergeActualBufferInventoryData(statTime, all, bufferSafeInventoryDfDOS).stream().map(item -> {
            //  库存金额 = 总库存 * 产品单价
            item.setInvPrice(AmountUtils.multiply(item.getInvTotalNum(), unitPrice));
            //  库存占比 = 库存 / 各种指标
            item.setInvRatio(AmountUtils.divideScale6(item.getInvTotalNum(), curDayTotalCapacity));

            String slaKey = Strings.join("@", item.getZoneName(), item.getInstanceType());
            if (slaMap.get(slaKey) != null) {
                item.setActualSerivceLevel(slaMap.get(slaKey).getSla());
            } else {
                item.setActualSerivceLevel(BigDecimal.ONE);
            }
            return item;
        }).collect(Collectors.toList()));
    }

    public Map<String, SlaResultItem> getSlaMap(String statDate) {

        WhereContent w = new WhereContent();
        w.andEqual("version", statDate.replaceAll("-", ""));
        w.groupBy("zone_name,instance_family");

        Map<String, SlaResultItem> rs = ckcldDBHelper.getAll(SlaDto.class, w.getSql(), w.getParams())
                .stream().collect(Collectors.toMap(t -> t.zone + "@" + t.ins, zdo -> {
                    BigDecimal slap = BigDecimal.ZERO;
                    BigDecimal soldRt = BigDecimal.ZERO;
                    if (zdo.getApiTotal().intValue() > 0) {
                        slap = slap.add(zdo.getApiSuccTotal().divide(zdo.getApiTotal(), 4, BigDecimal.ROUND_UP)
                                .multiply(BigDecimal.valueOf(0.5)));
                    }
                    if (zdo.getSumSold().intValue() > 0) {
                        soldRt = zdo.getSumSoldOut().divide(zdo.getSumSold(), 4, BigDecimal.ROUND_UP);
                        slap = slap.add(BigDecimal.valueOf(1.0000).subtract(soldRt).multiply(BigDecimal.valueOf(0.5)));
                    }

                    SlaResultItem item = new SlaResultItem();
                    item.setSla(slap);
                    item.setApiSuccessCores(zdo.getApiSuccTotal());
                    item.setApiTotalCores(zdo.getApiTotal());

                    if (item.getApiTotalCores() != null && item.getApiTotalCores().compareTo(BigDecimal.ZERO) > 0) {
                        item.setApiSuccessRate(NumberUtils.divide(item.getApiSuccessCores(), item.getApiTotalCores(), 4));
                    }

                    item.setApiSoldOut(zdo.getSumSoldOut());
                    item.setApiSold(zdo.getSumSold());

                    if (item.getApiSold() != null && item.getApiSold().compareTo(BigDecimal.ZERO) > 0) {
                        item.setApiSoldOutRate(NumberUtils.divide(item.getApiSoldOut(), item.getApiSold(), 4));
                    }
                    return item;

                }));
        return rs;
    }

    public List<OperationViewResp2.Item> mergeActualBufferInventoryData(String statTime, List<DwsActualInventoryDfDO> actualInventoryDfDOS, List<DwsBufferSafeInventoryDfDO> bufferSafeInventoryDfDOS) {
        return ListUtils.merge(actualInventoryDfDOS, bufferSafeInventoryDfDOS,
                o1 -> StringTools.join("@", o1.getInstanceType(), o1.getZoneName()),
                o2 -> StringTools.join("@", o2.getInstanceType(), o2.getZoneName()),
                (o1, o2) -> {
                    OperationViewResp2.Item item = new OperationViewResp2.Item();
                    item.setProductType("CVM");

                    if (ListUtils.isNotEmpty(o1)) {
                        item.setCustomhouseTitle(o1.get(0).getCustomhouseTitle());
                        item.setAreaName(o1.get(0).getAreaName());
                        item.setRegionName(o1.get(0).getRegionName());
                        item.setZoneName(o1.get(0).getZoneName());
                        item.setInstanceType(o1.get(0).getInstanceType());
                    } else {
                        item.setCustomhouseTitle(o2.get(0).getCustomhouseTitle());
                        item.setAreaName(o2.get(0).getAreaName());
                        item.setRegionName(o2.get(0).getRegionName());
                        item.setZoneName(o2.get(0).getZoneName());
                        item.setInstanceType(o2.get(0).getInstanceType());
                    }

                    //  总库存
                    item.setInvTotalNum(NumberUtils.sum(o1, o -> o.getActualInv()));
                    // 用户预扣库存
                    if (ListUtils.isNotEmpty(o1)) {
                        item.setPreDeductInv(NumberUtils.sum(o1.stream().filter(p -> p.getInvDetailType().equals("用户预扣")).collect(
                                Collectors.toList()), DwsActualInventoryDfDO::getActualInv));
                    }else {
                        item.setPreDeductInv(BigDecimal.ZERO);
                    }
                    // O1, O2 应该同时存在，如果 O2 不存在，则默认为 0，并告警
                    if (ListUtils.isEmpty(o2)) {
//                        this.taskLogService.genRunLog("getActualAndBufferInventory", "fillBaseFields", statTime + "->" + item.getZoneName() + "，机型：" + item.getInstanceType() + " 对应的弹性安全库存数据不存在");
                        BigDecimal bufferServiceLevel = this.getTargetServiceLevel(statTime, item.getZoneName(), item.getInstanceType(), "弹性");
                        //  弹性服务水平/系数
                        item.setBufferServiceLevel(bufferServiceLevel);
                        item.setBufferServiceLevelFactor(
                                BigDecimal.valueOf(OperationViewTools.normsinv(bufferServiceLevel.doubleValue())));
                        item.setBufferRate(BigDecimal.ONE);
                        item.setBufferRate(BigDecimal.ONE);
                        item.setBufferAverageCore(BigDecimal.ZERO);
                        item.setBufferSafetyInv(BigDecimal.ZERO);
                    } else {
                        DwsBufferSafeInventoryDfDO bufferSafeInventoryDfDO = o2.get(0);
                        //  弹性服务水平/系数
                        item.setBufferServiceLevel(bufferSafeInventoryDfDO.getBufferServiceLevel());
                        item.setBufferServiceLevelFactor(bufferSafeInventoryDfDO.getBufferServiceLevelFactor());
                        item.setBufferRoi(bufferSafeInventoryDfDO.getMckBufferRoi());
                        item.setBufferRate(bufferSafeInventoryDfDO.getMckBufferRate());
                        //  弹性规模日均值
                        item.setBufferAverageCore(bufferSafeInventoryDfDO.getBufferAverageCore());
                        //  弹性备货配额-三种算法目前结果完全相同，因此先抽取到这里
                        item.setBufferSafetyInv(bufferSafeInventoryDfDO.getFinalBufferSafetyInv());
                        //  弹性开始结束时间
                        item.setBufferAverageStartDate(bufferSafeInventoryDfDO.getBufferAverageStartDate());
                        item.setBufferAverageEndDate(bufferSafeInventoryDfDO.getBufferAverageEndDate());
                    }

                    return item;
                });
    }

    /**
     * 合并冗余字段
     */
//    public List<ActualInventoryAvgDTO> mergeData(String statTime, List<ActualInventoryAvgDTO> actualInv,
//                                              List<BufferAverageCoreDTO> bufferInv) {
//        return ListUtils.merge(actualInv, bufferInv,
//                o -> o.getGroupK(),
//                o -> o.getEntireGroupK(),
//                (o1, o2) -> {
//                    ActualInventoryDTO dto = new ActualInventoryDTO();
//                    dto.setDate(statTime);
//                    if (ListUtils.isNotEmpty(o1)) {
//                        dto.setCustomhouseTitle(o1.get(0).getCustomhouseTitle());
//                        dto.setAreaName(o1.get(0).getAreaName());
//                        dto.setRegionName(o1.get(0).getRegionName());
//                        dto.setZoneName(o1.get(0).getZoneName());
//                        dto.setInstanceType(o1.get(0).getInstanceType());
//                        dto.setTotalCore(NumberUtils.sum(o1, o -> o.getTotalCore()));
//                    } else {
//                        dto.setCustomhouseTitle(o2.get(0).getCustomhouseTitle());
//                        dto.setAreaName(o2.get(0).getAreaName());
//                        dto.setRegionName(o2.get(0).getRegionName());
//                        dto.setZoneName(o2.get(0).getZoneName());
//                        dto.setInstanceType(o2.get(0).getInstanceType());
//                        dto.setTotalCore(BigDecimal.ZERO);
//                    }
//
//                    if (ListUtils.isNotEmpty(o2)) {
//                        dto.setTotalBufferCore(NumberUtils.sum(o2, o -> o.getBufferAverageCore()));
//                    } else {
//                        dto.setTotalBufferCore(BigDecimal.ZERO);
//                    }
//                    return dto;
//                });
//    }

    /**
     * 获取某个产品当天的总容量
     */
    private BigDecimal getCurDayTotalCapacity(String statTime) {
        List<String> indicators = Lang.list("K", "Q", "D", "NS", "VC");
        String sql = "select sum(core_num) from report_cvm_jxc\n" +
                "where deleted = 0 and product_type = ? and indicator_code in (?) and stat_time = ?";
        List<BigDecimal> raw =
                rrpDBHelper.getRaw(BigDecimal.class, sql, "CVM", indicators, statTime);
        return raw.get(0) == null ? BigDecimal.ZERO : raw.get(0);
    }

    private Map<String, String> getWeekInterval(String statTime, int n) {
        Map<String, String> map = new HashMap<>();
        Tuple2<String, String> timeInterval = commonService.getTimeIntervalByWeek(statTime, n);
        String startDate = timeInterval._1;
        String endDate = DateUtils.formatDate(DateUtils.addTime(DateUtils.parse(startDate), Calendar.DATE, 6));
        for (int i = n; i >= 1; i--) {
            map.put("w-" + i, startDate + "~" + endDate);
            startDate = DateUtils.formatDate(DateUtils.addTime(DateUtils.parse(startDate), Calendar.DATE, 7));
            endDate = DateUtils.formatDate(DateUtils.addTime(DateUtils.parse(endDate), Calendar.DATE, 7));
        }

        statTime = DateUtils.formatDate(DateUtils.parse(statTime));
        List<HolidayWeekInfoDTO> holidayWeekInfo = ListUtils.newList();
        // 当周也算
        ResPlanHolidayWeekDO curWeek = baseDictService.getHolidayWeekInfoByDate(statTime);
        HolidayWeekInfoDTO curWeekDTO = new HolidayWeekInfoDTO();
        curWeekDTO.setYear(curWeek.getYear());
        curWeekDTO.setMonth(curWeek.getMonth());
        curWeekDTO.setWeek(curWeek.getWeek());
        curWeekDTO.setStartDate(curWeek.getStart());
        curWeekDTO.setEndDate(curWeek.getEnd());
        curWeekDTO.setWeekNFromNow(0);
        holidayWeekInfo.add(curWeekDTO);
        // 未来13周
        List<HolidayWeekInfoDTO> holidayWeekInfoDTOS = dictService.getHolidayWeekInfoBase(LocalDate.parse(statTime), 13);
        holidayWeekInfo.addAll(holidayWeekInfoDTOS);

        for (HolidayWeekInfoDTO dto : holidayWeekInfo) {
            map.put("w" + dto.getWeekNFromNow(), dto.getStartDate() + "~" + dto.getEndDate());
        }

        return map;
    }

    private Map<String, List<DwsInventoryHealthWeeklyDeliveryDfDO>> getDeliveryData(OperationViewReq2 req, boolean enableDelivery) {
        if (!enableDelivery) {
            return Collections.emptyMap();
        }

        String statTime = DateUtils.formatDate(req.getDate());
        WhereSQL condition = req.genCondition();
        condition.and("stat_time = ?", statTime);
        condition.and("week_index < 0");
        WhereSQL categoryCondition = genCategoryCondition(req.getZoneCategory(), req.getInstanceTypeCategory(), req.getIsCombine(), req.getCategoryDate(), req.getCustomhouseTitle());
        condition.and(categoryCondition);

        List<DwsInventoryHealthWeeklyDeliveryDfDO> deliveryDfDOS = ckcldDBHelper.getAll(DwsInventoryHealthWeeklyDeliveryDfDO.class, condition.getSQL(), condition.getParams());
        return ListUtils.toMapList(deliveryDfDOS, o -> o.toKey(), o -> o);
    }

    public List<BigDecimal> calcDeliveryData(Map<String, List<DwsInventoryHealthWeeklyDeliveryDfDO>> deliveryDfMap, String key) {
        List<DwsInventoryHealthWeeklyDeliveryDfDO> deliveryDf = deliveryDfMap.get(key);

        if (deliveryDf != null && ListUtils.isNotEmpty(deliveryDf)) {
            deliveryDf.stream().forEach(o -> {
                if (DateUtils.formatDate(o.getXyCreateTime()).equals("1970-01-01")) {
                    o.setSla(o.getSla().subtract(o.getXyApprovalDays()));
                    o.setXyApprovalDays(BigDecimal.ZERO);
                }
            });
        }

        if (deliveryDf != null && ListUtils.isNotEmpty(deliveryDf)) {
            // 13 周交付周期平均值 = sum(sla*num) / sum(num)
            BigDecimal totalDays = NumberUtils.sum(deliveryDf, o -> o.getSla().multiply(BigDecimal.valueOf(o.getNum())));
            BigDecimal totalNum = NumberUtils.sum(deliveryDf, o -> o.getNum());
            BigDecimal averageDays = totalDays.divide(totalNum, 2, BigDecimal.ROUND_HALF_UP);
//            result.setDeliveryAvg(averageDays);
            // 求标准差
            List<BigDecimal> dataArr = ListUtils.newList();
            deliveryDf.forEach(o -> {
                for (int i = 0; i < o.getNum(); i++) {
                    dataArr.add(o.getSla());
                }
            });
            BigDecimal sd = OperationViewTools.calculateSD(dataArr);
//            result.setDeliveryStandardDiff(sd);
            return ListUtils.newList(averageDays, sd);
        } else {
            // 供应周期数据为 配置的 SLA
//            result.setDeliveryAvg(BigDecimal.valueOf(result.getSla()));
//            result.setDeliveryStandardDiff(null);
            return null;
        }
    }

    private void setDeliveryData(OperationViewResp2.SafetyInventoryResult result, Map<String, List<DwsInventoryHealthWeeklyDeliveryDfDO>> deliveryDfMap, String key) {
        List<BigDecimal> deliveryData = calcDeliveryData(deliveryDfMap, key);
        if (deliveryData != null) {
            result.setDeliveryAvg(deliveryData.get(0));
            result.setDeliveryStandardDiff(deliveryData.get(1));
        } else {
            result.setDeliveryAvg(BigDecimal.valueOf(result.getSla()));
            result.setDeliveryStandardDiff(null);
        }
    }

    public void buildHistoryWeekPeak(OperationViewReq2 req, OperationViewResp2 resp) {
        String customerCustomGroup = req.getCustomerCustomGroup();
        CustomerCustomGroupEnum groupEnum = CustomerCustomGroupEnum.getByCode(customerCustomGroup);
        if (groupEnum == null) {
            groupEnum = CustomerCustomGroupEnum.ALL;
        }

        String statTime = DateUtils.formatDate(req.getDate());
        LocalDate date = LocalDate.parse(statTime);
        String monday = DateUtils.formatDate(date.with(DayOfWeek.MONDAY));
        WhereSQL condition = req.genCondition();
        condition.and("week_index < 0 and product_type = 'CVM'");
        condition.and("customer_custom_group = ?", groupEnum.getName());
        if (ListUtils.isNotEmpty(req.getExcludeUinList())) {
            condition.and("exclude_uin_list = ?", req.handleExcludeUinList());
        } else {
            condition.and("exclude_uin_list = '(空值)'");
        }
        WhereSQL monCondition = condition.copy();
        condition.and("stat_time = ?", statTime);
        monCondition.and("stat_time = ?", monday);

        WhereSQL categoryCondition = genCategoryCondition(req.getZoneCategory(), req.getInstanceTypeCategory(), req.getIsCombine(), req.getCategoryDate(), req.getCustomhouseTitle());
        condition.and(categoryCondition);

        List<DwsInventoryHealthWeeklyScaleDfDO> all = ckcldDBHelper.getAll(
                DwsInventoryHealthWeeklyScaleDfDO.class, condition.getSQL(), condition.getParams());
        List<DwsInventoryHealthWeeklyScaleDfDO> allMon = ckcldDBHelper.getAll(
                DwsInventoryHealthWeeklyScaleDfDO.class, monCondition.getSQL(), monCondition.getParams());

        Map<String, List<DwsInventoryHealthWeeklyScaleDfDO>> mapList = ListUtils.toMapList(
                all, o -> toKey(o), o -> o);
        Map<String, List<DwsInventoryHealthWeeklyScaleDfDO>> mapMonList = ListUtils.toMapList(
                allMon, o -> toKey(o), o -> o);

        boolean enableDelivery = isDeliveryEnabledForAlgorithm();
        OperationViewReq2 newReq = createNewReq(req, monday);
        Map<String, List<DwsInventoryHealthWeeklyDeliveryDfDO>> deliveryDfMap = getDeliveryData(req, enableDelivery);
        Map<String, List<DwsInventoryHealthWeeklyDeliveryDfDO>> deliveryDfMonMap = getDeliveryData(newReq, enableDelivery);

        List<OperationViewResp2.Item> items = resp.getData();
        Map<String, List<OperationViewResp2.Item>> itemMap = ListUtils.toMapList(items, o -> o.toKey(), o -> o);
        Map<String, BigDecimal> bufferMap = ListUtils.toMap(items, o -> o.toKey(), o -> o.getBufferSafetyInv());

        // 周转库存
        Map<String, List<DwsInventoryHealthMckTurnoverWfDO>> turnoverDfDOMap = getTurnoverInventoryWeek13Avg(req);
        Map<String, DwsInventoryHealthMckTurnoverWfDO> turnoverCurWeekDOMap = getTurnoverInventoryCurWeek(req);
        boolean isTurnoverInvEmpty = ListUtils.isEmpty(turnoverCurWeekDOMap);

        // 预测周转库存
        List<DwsInventoryHealthMckForecastTurnoverDfAnyDO> forecastTurnoverDfDOS = getForecastTurnoverInventory(req);
        Map<String, List<DwsInventoryHealthMckForecastTurnoverDfAnyDO>> forecastTurnoverMap = ListUtils.toMapList(
                forecastTurnoverDfDOS,
                item -> StringTools.join("@", item.getProductType(), item.getZoneName(), item.getInstanceType()),
                o -> o
        );

        // 安全人工调整
        Map<String, BigDecimal> manualConfigMap =
                SpringUtil.getBean(ManualConfigServiceImpl.class).querySnapshotManual(statTime);
        List<OperationViewResp2.Item> itemBatch = Lang.list();

        for (Map.Entry<String, List<DwsInventoryHealthWeeklyScaleDfDO>> e : mapList.entrySet()) {
            OperationViewResp2.Item item = new OperationViewResp2.Item();
            item.setProductType(e.getValue().get(0).getProductType());
            item.setCustomhouseTitle(e.getValue().get(0).getCustomhouseTitle());
            item.setAreaName(e.getValue().get(0).getAreaName());
            item.setRegionName(e.getValue().get(0).getRegionName());
            item.setZoneName(e.getValue().get(0).getZoneName());
            item.setInstanceType(e.getValue().get(0).getInstanceType());
            item.setInvTotalNum(NumberUtils.sum(itemMap.get(e.getKey()), o -> o.getInvTotalNum()));
            item.setBufferSafetyInv(bufferMap.get(e.getKey()));

            OperationViewResp2.SafetyInventoryResult historyWeekPeak = new OperationViewResp2.SafetyInventoryResult();
            item.setHistoryWeekPeak(historyWeekPeak);

            //  交付SLA
            BigDecimal sla = BigDecimal.valueOf(operationViewService.getSLAByInstanceType(item.getInstanceType(),
                    Objects.equals(item.getCustomhouseTitle(), "境内")));
            historyWeekPeak.setSla(sla.intValue());

            // 13周交付周期
            setDeliveryData(historyWeekPeak, deliveryDfMap, e.getKey());
            List<BigDecimal> deliveryData = calcDeliveryData(deliveryDfMonMap, e.getKey());
            BigDecimal deliveryAvg;
            BigDecimal deliveryStandardMonDiff;
            if (deliveryData != null) {
                deliveryAvg = deliveryData.get(0);
                deliveryStandardMonDiff = deliveryData.get(1);
            } else {
                deliveryAvg = BigDecimal.valueOf(historyWeekPeak.getSla());
                deliveryStandardMonDiff = null;
            }

            // 安全库存人工调整
            String manualConfigKey = Strings.join("@", item.getZoneName(), item.getInstanceType());
            historyWeekPeak.setSafeInvManualConfig(manualConfigMap.getOrDefault(manualConfigKey, BigDecimal.ZERO));

            historyWeekPeak.setAlgorithm("历史周峰");
            historyWeekPeak.setWeekDemandMap(ListUtils.toMap(e.getValue(), o -> "w" + (o.getWeekIndex()), o ->
                    o.getWeekPeakLogicNum().setScale(2, BigDecimal.ROUND_HALF_UP)));
            fillWeekDemandMap(historyWeekPeak.getWeekDemandMap());
            List<BigDecimal> demands = ListUtils.transform(e.getValue(), o -> o.getWeekPeakLogicNum());
            BigDecimal demandSD = OperationViewTools.calculateSD(demands);
            historyWeekPeak.setStandardDiff(demandSD);
            List<BigDecimal> monDemands = ListUtils.transform(mapMonList.get(e.getKey()), o -> o.getWeekPeakLogicNum());
            BigDecimal demandMonSD = OperationViewTools.calculateSD(monDemands);



            //  服务水平
            BigDecimal serviceLevel = this.getTargetServiceLevel(statTime, item.getZoneName(), item.getInstanceType(), "包年包月");
            BigDecimal serviceMonLevel = this.getTargetServiceLevel(monday, item.getZoneName(), item.getInstanceType(), "包年包月");
            historyWeekPeak.setServiceLevel(serviceLevel);
            //  服务系数
            double serviceNum = OperationViewTools.normsinv(serviceLevel.doubleValue());
            historyWeekPeak.setServiceLevelFactor(BigDecimal.valueOf(serviceNum));
            double serviceMonNum = OperationViewTools.normsinv(serviceMonLevel.doubleValue());

            //  弹性服务水平
            BigDecimal bufferServiceLevel = this.getTargetServiceLevel(statTime, item.getZoneName(), item.getInstanceType(), "弹性");
            historyWeekPeak.setBufferServiceLevel(bufferServiceLevel);

            //  弹性服务系数
            double bufferServiceNum =
                    OperationViewTools.normsinv(historyWeekPeak.getBufferServiceLevel().doubleValue());
            historyWeekPeak.setBufferServiceLevelFactor(BigDecimal.valueOf(bufferServiceNum));

            BigDecimal avgDemandNum = NumberUtils.avg(demands, 6);
            BigDecimal avgDemandMonNum = NumberUtils.avg(monDemands, 6);

            //  包月安全库存
            BigDecimal finalSla = deliveryAvg != null ? deliveryAvg : sla;
            BigDecimal deliveryStardardDiff = null;
            if (enableDelivery) {
                deliveryStardardDiff = deliveryStandardMonDiff != null ? deliveryStandardMonDiff : BigDecimal.ZERO;
            }
            BigDecimal monthlyInvNum = calMonthlyInvNum(serviceMonNum, finalSla, demandMonSD, avgDemandMonNum, deliveryStardardDiff);
            historyWeekPeak.setMonthlySafetyInv(monthlyInvNum);

            //  安全库存 = 包月安全库存 + 弹性备货配额 + 人工调整
            historyWeekPeak.setSafetyInv(NumberUtils.max(AmountUtils.add(historyWeekPeak.getMonthlySafetyInv(),
                    item.getBufferSafetyInv() != null ? item.getBufferSafetyInv() : BigDecimal.ZERO).add(historyWeekPeak.getSafeInvManualConfig()), BigDecimal.ZERO));

            //  周转库存
//            BigDecimal invCacheNum = avgDemandNum.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : avgDemandNum;
            historyWeekPeak.setDemandAvg(avgDemandNum);
//            historyWeekPeak.setTurnoverInv(invCacheNum);
            List<DwsInventoryHealthMckTurnoverWfDO> mckTurnoverDfDOS = turnoverDfDOMap.get(StringTools.join("@", item.getProductType(), item.getZoneName(), item.getInstanceType()));
            historyWeekPeak.setTurnoverInv(NumberUtils.avg(mckTurnoverDfDOS, 6, o -> o.getTurnoverInv()));
            historyWeekPeak.setTurnoverInvWeekPeak(NumberUtils.avg(mckTurnoverDfDOS, 6, o -> o.getWeekPeakCore()));
            historyWeekPeak.setTurnoverInvReserved(NumberUtils.avg(mckTurnoverDfDOS, 6, o -> o.getWeekReservedAvgCore()));

            if (!isTurnoverInvEmpty) {
                DwsInventoryHealthMckTurnoverWfDO mckTurnoverWfDO = turnoverCurWeekDOMap.get(StringTools.join("@", item.getProductType(), item.getZoneName(), item.getInstanceType()));
                if (mckTurnoverWfDO != null) {
                    historyWeekPeak.setTurnoverInvCurWeek(mckTurnoverWfDO.getTurnoverInv());
                    historyWeekPeak.setTurnoverInvCurWeekPeak(mckTurnoverWfDO.getWeekPeakCore());
                    historyWeekPeak.setTurnoverInvCurWeekAvgReserved(mckTurnoverWfDO.getWeekReservedAvgCore());
                }
            }

            // 预测周转库存
            List<DwsInventoryHealthMckForecastTurnoverDfAnyDO> forecastTurnoverDfDOList = forecastTurnoverMap.get(StringTools.join("@", item.getProductType(), item.getZoneName(), item.getInstanceType()));
            historyWeekPeak.setForecastTurnOverInvMap(ListUtils.toMap(forecastTurnoverDfDOList, o -> "w" + (o.getWeekIndex()), o -> {
                Map<String, BigDecimal> map = new HashMap<>();
                map.put("turnoverInv", o.getTurnoverInv());
                map.put("weekPeakCore", o.getWeekPeakCore());
                map.put("avgForecastCore", o.getAvgForecastCore());
                map.put("totalForecastCore", o.getTotalForecastCore());
                return map;
            }));

            itemBatch.add(item);
        }

        resp.putHistoryWeekPeakToItem(itemBatch);
    }

    private List<DwsInventoryHealthWeeklyScaleDfAnyDO> getInventoryHealthWeeklyScales(OperationViewReq2 req) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("getInventoryHealthWeeklyScales"+"sql生成");
        String customerCustomGroup = req.getCustomerCustomGroup();
        CustomerCustomGroupEnum groupEnum = CustomerCustomGroupEnum.getByCode(customerCustomGroup);
        if (groupEnum == null) {
            groupEnum = CustomerCustomGroupEnum.ALL;
        }

        String statTime = DateUtils.formatDate(req.getDate());

        WhereSQL condition = req.genCondition();
        condition.and("stat_time = ?", statTime);
        condition.and("week_index < 0 and product_type = 'CVM'");
        condition.and("customer_custom_group = ?", groupEnum.getName());
        if (ListUtils.isNotEmpty(req.getExcludeUinList())) {
            condition.and("exclude_uin_list = ?", req.handleExcludeUinList());
        } else {
            condition.and("exclude_uin_list = '(空值)'");
        }

        WhereSQL categoryCondition = genCategoryCondition(req.getZoneCategory(), req.getInstanceTypeCategory(), req.getIsCombine(), req.getCategoryDate(), req.getCustomhouseTitle());
        condition.and(categoryCondition);
        // 聚合分组
        condition.addGroupBy("stat_time","holiday_week_start_date","week_index","holiday_week_end_date","product_type","instance_type","customhouse_title","area_name","region_name","zone_name");
        stopWatch.stop();

        stopWatch.start("数据获取" + statTime);
        List<DwsInventoryHealthWeeklyScaleDfAnyDO> all = ckcldDBHelper.getAll(
                DwsInventoryHealthWeeklyScaleDfAnyDO.class, condition.getSQL(), condition.getParams());
        stopWatch.stop();
        log.info(stopWatch.prettyPrint());
        return all;
    }
    @Data
    @Table("dws_inventory_health_weekly_scale_df")
    // 原 DO:DwsInventoryHealthWeeklyScaleDfDO
    public static class DwsInventoryHealthWeeklyScaleDfAnyDO{
        /** 统计日期<br/>Column: [stat_time] */
        @Column(value = "stat_time")
        private LocalDate statTime;


        /** 节假周开始时间<br/>Column: [holiday_week_start_date] */
        @Column(value = "holiday_week_start_date")
        private LocalDate holidayWeekStartDate;

        @Column(value = "week_index")
        private Integer weekIndex;

        /** 节假周结束时间<br/>Column: [holiday_week_end_date] */
        @Column(value = "holiday_week_end_date")
        private LocalDate holidayWeekEndDate;

        /** 产品类型<br/>Column: [product_type] */
        @Column(value = "product_type")
        private String productType;

        /** 实例类型<br/>Column: [instance_type] */
        @Column(value = "instance_type")
        private String instanceType;

        /** 境内外<br/>Column: [customhouse_title] */
        @Column(value = "customhouse_title")
        private String customhouseTitle;

        /** 地域名称 华南地区<br/>Column: [area_name] */
        @Column(value = "area_name")
        private String areaName;

        /** 地域中文名称 广州<br/>Column: [region_name] */
        @Column(value = "region_name")
        private String regionName;

        /** 可用区名 广州四区<br/>Column: [zone_name] */
        @Column(value = "zone_name")
        private String zoneName;


        /** 周峰逻辑数<br/>Column: [week_peak_logic_num] */
        @Column(value = "sum_week_peak_logic_num",computed = "sum(week_peak_logic_num)")
        private BigDecimal weekPeakLogicNum;

    }

    /**
     * 拿到过去 13 周，每一周的 weekN 预测量，按照机型+境内外配置 N 的值
     * @param req
     * @return
     */
    private List<DwsDemandWeekNPplVersionItemDO> getPplForecastWeekN(OperationViewReq2 req) {
        String statTime = DateUtils.formatDate(req.getDate());
        WhereSQL condition = req.genCondition();
        condition.and("stat_time = ?", statTime);
        condition.and("week_index < 0 and product = 'CVM'");

        WhereSQL categoryCondition = genCategoryCondition(req.getZoneCategory(), req.getInstanceTypeCategory(), req.getIsCombine(), req.getCategoryDate(), req.getCustomhouseTitle());
        condition.and(categoryCondition);

        List<DwsDemandWeekNPplVersionItemDO> result = ckcldDBHelper.getAll(DwsDemandWeekNPplVersionItemDO.class, condition.getSQL(), condition.getParams());
        return result;
    }

    /**
     * 过去 13 周的周转库存均值
     * @param req
     * @return
     */
    private Map<String, List<DwsInventoryHealthMckTurnoverWfDO>> getTurnoverInventoryWeek13Avg(OperationViewReq2 req) {
        String statTime = DateUtils.formatDate(req.getDate());
        WhereSQL condition = req.genCondition();
        List<HolidayWeekInfoDTO> holidayWeekInfoDTOS = dictService.getHolidayWeekInfoBase(LocalDate.parse(statTime), -13);
        // 获取开始和结束的 year week
        condition.and("holiday_week_start_date between ? and ?", holidayWeekInfoDTOS.get(0).getStartDate(),  holidayWeekInfoDTOS.get(holidayWeekInfoDTOS.size() - 1).getStartDate());
        condition.and("product_type = 'CVM'");

        WhereSQL categoryCondition = genCategoryCondition(req.getZoneCategory(), req.getInstanceTypeCategory(), req.getIsCombine(), req.getCategoryDate(), req.getCustomhouseTitle());
        condition.and(categoryCondition);

        List<DwsInventoryHealthMckTurnoverWfDO> result = ckcldDBHelper.getAll(DwsInventoryHealthMckTurnoverWfDO.class, condition.getSQL(), condition.getParams());

        Map<String, List<DwsInventoryHealthMckTurnoverWfDO>> resultMap = ListUtils.toMapList(result, o -> StringTools.join("@", o.getProductType(), o.getZoneName(), o.getInstanceType()), o -> o);

        // 如果有一周没有值，则填充一条 0，确保均值跟周转周数计算的相同。
        for (Map.Entry<String, List<DwsInventoryHealthMckTurnoverWfDO>> entry : resultMap.entrySet()) {
            List<DwsInventoryHealthMckTurnoverWfDO> turnoverWfDOS = entry.getValue();
            Map<String, DwsInventoryHealthMckTurnoverWfDO> map = ListUtils.toMap(turnoverWfDOS, o -> o.getYearWeek(), o -> o);

            for (HolidayWeekInfoDTO infoDTO : holidayWeekInfoDTOS) {
                String yearWeek = infoDTO.getYear().toString() + infoDTO.getWeek().toString();
                if (!map.containsKey(yearWeek)) {
                    DwsInventoryHealthMckTurnoverWfDO turnoverWfDO = new DwsInventoryHealthMckTurnoverWfDO();
                    turnoverWfDO.setYearWeek(yearWeek);
                    turnoverWfDO.setHolidayYear(infoDTO.getYear());
                    turnoverWfDO.setHolidayMonth(infoDTO.getMonth());
                    turnoverWfDO.setHolidayWeek(infoDTO.getWeek());
                    turnoverWfDO.setHolidayWeekStartDate(LocalDate.parse(infoDTO.getStartDate()));
                    turnoverWfDO.setHolidayWeekEndDate(LocalDate.parse(infoDTO.getEndDate()));
                    turnoverWfDO.setProductType(turnoverWfDOS.get(0).getProductType());
                    turnoverWfDO.setInstanceType(turnoverWfDOS.get(0).getInstanceType());
                    turnoverWfDO.setCustomhouseTitle(turnoverWfDOS.get(0).getCustomhouseTitle());
                    turnoverWfDO.setAreaName(turnoverWfDOS.get(0).getAreaName());
                    turnoverWfDO.setRegionName(turnoverWfDOS.get(0).getRegionName());
                    turnoverWfDO.setZoneName(turnoverWfDOS.get(0).getZoneName());
                    turnoverWfDO.setTurnoverInv(BigDecimal.ZERO);
                    turnoverWfDO.setWeekPeakCore(BigDecimal.ZERO);
                    turnoverWfDO.setWeekReservedAvgCore(BigDecimal.ZERO);

                    turnoverWfDOS.add(turnoverWfDO);
                }
            }
        }

        return resultMap;
    }

    /**
     * 过去 13 周的周转库存均值(v2 版本,进行了 SQL 优化,耗时点:数量级,字段)
     * @param req
     * @return
     */
    public Map<String, List<DwsInventoryHealthMckTurnoverWfAnyDO>> getTurnoverInventoryWeek13AvgV2(OperationViewReq2 req) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("getTurnoverInventoryWeek13AvgV2"+"sql生成");
        String statTime = DateUtils.formatDate(req.getDate());
        WhereSQL condition = req.genCondition();
        List<HolidayWeekInfoDTO> holidayWeekInfoDTOS = dictService.getHolidayWeekInfoBase(LocalDate.parse(statTime), -13);
        // 获取开始和结束的 year week
        condition.and("holiday_week_start_date between ? and ?", holidayWeekInfoDTOS.get(0).getStartDate(),  holidayWeekInfoDTOS.get(holidayWeekInfoDTOS.size() - 1).getStartDate());
        condition.and("product_type = 'CVM'");

        WhereSQL categoryCondition = genCategoryCondition(req.getZoneCategory(), req.getInstanceTypeCategory(), req.getIsCombine(), req.getCategoryDate(), req.getCustomhouseTitle());
        condition.and(categoryCondition);
        condition.addGroupBy("product_type","year_week","instance_type","zone_name");
        stopWatch.stop();
        stopWatch.start("数据获取" + statTime);
        List<DwsInventoryHealthMckTurnoverWfAnyDO> result = ckcldDBHelper.getAll(DwsInventoryHealthMckTurnoverWfAnyDO.class, condition.getSQL(), condition.getParams());
        stopWatch.stop();
        Map<String, List<DwsInventoryHealthMckTurnoverWfAnyDO>> resultMap = ListUtils.toMapList(result, o -> StringTools.join("@", o.getProductType(), o.getZoneName(), o.getInstanceType()), o -> o);

        // 如果有一周没有值，则填充一条 0，确保均值跟周转周数计算的相同。
        for (Map.Entry<String, List<DwsInventoryHealthMckTurnoverWfAnyDO>> entry : resultMap.entrySet()) {
            List<DwsInventoryHealthMckTurnoverWfAnyDO> turnoverWfDOS = entry.getValue();
            Map<String, DwsInventoryHealthMckTurnoverWfAnyDO> map = ListUtils.toMap(turnoverWfDOS, o -> o.getYearWeek(), o -> o);

            for (HolidayWeekInfoDTO infoDTO : holidayWeekInfoDTOS) {
                String yearWeek = infoDTO.getYear().toString() + infoDTO.getWeek().toString();
                if (!map.containsKey(yearWeek)) {
                    DwsInventoryHealthMckTurnoverWfAnyDO turnoverWfDO = new DwsInventoryHealthMckTurnoverWfAnyDO();
                    turnoverWfDO.setYearWeek(yearWeek);
                    turnoverWfDO.setProductType(turnoverWfDOS.get(0).getProductType());
                    turnoverWfDO.setInstanceType(turnoverWfDOS.get(0).getInstanceType());
                    turnoverWfDO.setZoneName(turnoverWfDOS.get(0).getZoneName());
                    turnoverWfDO.setTurnoverInv(BigDecimal.ZERO);
                    turnoverWfDO.setWeekPeakCore(BigDecimal.ZERO);
                    turnoverWfDO.setWeekReservedAvgCore(BigDecimal.ZERO);

                    turnoverWfDOS.add(turnoverWfDO);
                }
            }
        }
        log.info(stopWatch.prettyPrint());
        return resultMap;
    }

    @Data
    @Table("dws_inventory_health_mck_turnover_wf")
    // 原DO:DwsInventoryHealthMckTurnoverWfDO, 上层只用到了product_type@yearWeek@instanceType@zoneName进行聚合分组,其他字段可以忽略
    public static class DwsInventoryHealthMckTurnoverWfAnyDO{
        @Column("product_type")
        private String productType;
        @Column("year_week")
        private String yearWeek;
        @Column("instance_type")
        private String instanceType;
        @Column("zone_name")
        private String zoneName;

        /**
         * 周转库存量
         */
        @Column(value = "any_turnover_inv",computed = "sum(turnover_inv)")
        private BigDecimal turnoverInv;

        /**
         * 周峰
         */
        @Column(value = "any_week_peak_core",computed = "sum(week_peak_core)")
        private BigDecimal weekPeakCore;
        /**
         * 预扣均值
         */
        @Column(value = "any_week_reserved_avg_core",computed = "sum(week_reserved_avg_core)")
        private BigDecimal weekReservedAvgCore;

    }


    private Map<String, DwsInventoryHealthMckTurnoverWfDO> getTurnoverInventoryCurWeek(OperationViewReq2 req) {
        String statTime = DateUtils.formatDate(req.getDate());
        WhereSQL condition = req.genCondition();
        ResPlanHolidayWeekDO holidayWeekDO = baseDictService.getHolidayWeekInfoByDate(statTime);
        condition.and("year_week = ?", holidayWeekDO.getYear().toString() + holidayWeekDO.getWeek().toString());
        condition.and("product_type = 'CVM'");

        WhereSQL categoryCondition = genCategoryCondition(req.getZoneCategory(), req.getInstanceTypeCategory(), req.getIsCombine(), req.getCategoryDate(), req.getCustomhouseTitle());
        condition.and(categoryCondition);
        List<DwsInventoryHealthMckTurnoverWfDO> result = ckcldDBHelper.getAll(DwsInventoryHealthMckTurnoverWfDO.class, condition.getSQL(), condition.getParams());
        return ListUtils.toMap(result, o -> StringTools.join("@", o.getProductType(), o.getZoneName(), o.getInstanceType()), o -> o);
    }

    private List<DwsInventoryHealthMckForecastTurnoverDfAnyDO> getForecastTurnoverInventory(OperationViewReq2 req) {
        StopWatch stopWatch = new StopWatch();
        String statTime = DateUtils.formatDate(req.getDate());
        stopWatch.start("getForecastTurnoverInventory"+"sql生成");
        WhereSQL condition = req.genCondition();
        condition.and("stat_time = ?", statTime);
        condition.and("product_type = 'CVM'");

        WhereSQL categoryCondition = genCategoryCondition(req.getZoneCategory(), req.getInstanceTypeCategory(), req.getIsCombine(), req.getCategoryDate(), req.getCustomhouseTitle());
        condition.and(categoryCondition);
        stopWatch.stop();
        stopWatch.start("数据获取" + statTime);
        List<DwsInventoryHealthMckForecastTurnoverDfAnyDO> all = ckcldDBHelper.getAll(DwsInventoryHealthMckForecastTurnoverDfAnyDO.class, condition.getSQL(), condition.getParams());
        stopWatch.stop();
        log.info(stopWatch.prettyPrint());
        return all;
    }

    @Table("dws_inventory_health_mck_forecast_turnover_df")
    @Data
    public static class DwsInventoryHealthMckForecastTurnoverDfAnyDO {
        //原DO:DwsInventoryHealthMckForecastTurnoverDfDO 删去一些不需要的字段
        @Column("instance_type")
        private String instanceType;
        @Column("zone_name")
        private String zoneName;
        @Column("week_index")
        private Integer weekIndex;
        @Column("product_type")
        private String productType;
        /**
         * 预测周转库存=（T0周过去12周周峰均值*12+最新版本的本周预测净增值）/13
         */
        @Column("turnover_inv")
        private BigDecimal turnoverInv;

        /**
         * T0周过去12周周峰均值
         */
        @Column("week_peak_avg12_core")
        private BigDecimal weekPeakCore;
        /**
         * WN 预测净增值，均摊之后
         */
        @Column("avg_forecast_core")
        private BigDecimal avgForecastCore;

        @Column("total_forecast_core")
        private BigDecimal totalForecastCore;

    }

    private List<DwsInventoryHealthMckForecastMonthlySafeDfAnyDO> getForecastMonthlySafeInventory(OperationViewReq2 req) {
        StopWatch stopWatch = new StopWatch();
        String statTime = DateUtils.formatDate(req.getDate());
        stopWatch.start("getForecastMonthlySafeInventory"+"sql生成");
        WhereSQL condition = req.genCondition();
        condition.and("stat_time = ?", statTime);
        condition.and("product_type = 'CVM'");

        WhereSQL categoryCondition = genCategoryCondition(req.getZoneCategory(), req.getInstanceTypeCategory(), req.getIsCombine(), req.getCategoryDate(), req.getCustomhouseTitle());
        condition.and(categoryCondition);
        stopWatch.stop();
        stopWatch.start("数据获取" + statTime);
        List<DwsInventoryHealthMckForecastMonthlySafeDfAnyDO> all = ckcldDBHelper.getAll(DwsInventoryHealthMckForecastMonthlySafeDfAnyDO.class, condition.getSQL(), condition.getParams());
        stopWatch.stop();
        log.info(stopWatch.prettyPrint());
        return all;
    }

    @Table("dws_inventory_health_mck_forecast_monthly_safe_df")
    @Data
    public static class DwsInventoryHealthMckForecastMonthlySafeDfAnyDO {
        //原DO:DwsInventoryHealthMckForecastMonthlySafeDfDO 删除不需要的字段
        @Column("product_type")
        private String productType;
        @Column("instance_type")
        private String instanceType;
        @Column("zone_name")
        private String zoneName;
        @Column("week_index")
        private Integer weekIndex;

        /**
         * 预测安全库存预估=周转周数M*预测周转库存
         */
        @Column("monthly_safe_inv")
        private BigDecimal monthlySafeInv;

        /**
         * 预测安全库存预估=周转周数M*预测周转库存 - 不考虑供应波动
         */
        @Column("no_delivery_monthly_safe_inv")
        private BigDecimal noDeliveryMonthlySafeInv;

        /**
         * 安全库存的周转周数M=13周安全库存均值/13周实际周峰净增均值
         */
        @Column("turnover_week_num")
        private BigDecimal turnoverWeekNum;

        /**
         * 安全库存的周转周数M=13周安全库存均值/13周实际周峰净增均值 - 不考虑供应波动
         */
        @Column("no_delivery_turnover_week_num")
        private BigDecimal noDeliveryTurnoverWeekNum;

        /**
         * 预测周转库存
         */
        @Column("turnover_inv")
        private BigDecimal turnoverInv;

        /**
         * 13周安全库存均值
         */
        @Column("safe_inv_avg_13")
        private BigDecimal safeInvAvg13;

        /**
         * 13周安全库存均值 - 不考虑供应波动
         */
        @Column("no_delivery_safe_inv_avg_13")
        private BigDecimal noDeliverySafeInvAvg13;

        /**
         * 13周实际周峰净增均值
         */
        @Column("week_peak_avg_13")
        private BigDecimal weekPeakAvg13;
    }

    /**
     * 历史周峰算法的变体，与历史周峰算法的差别是，使用 N 周（例如5或者8，可配置）前的需求预测来衡量需求不确定性，而不是过去13周实际需求的均值来衡量
     * @param req
     * @param resp
     */
    public void buildHistoryWeekPeakDemand(OperationViewReq2 req, OperationViewResp2 resp) {
        StopWatch stopWatch = new StopWatch();
        String statTime = DateUtils.formatDate(req.getDate());
        LocalDate date = LocalDate.parse(statTime);
        String monday = DateUtils.formatDate(date.with(DayOfWeek.MONDAY));
        OperationViewReq2 newReq = createNewReq(req, monday);
        //1.线程池异步获取数据
        stopWatch.start("线程池提交任务" + statTime);
        //获取历史周峰数据
        Future<List<DwsInventoryHealthWeeklyScaleDfAnyDO>> scaleFuture = threadPool.submit(() -> getInventoryHealthWeeklyScales(req));
        //获取供应波动
        boolean enableDelivery = isDeliveryEnabledForAlgorithm();
        Future<Map<String, List<DwsInventoryHealthWeeklyDeliveryDfDO>>> deliveryFuture =threadPool.submit(() -> getDeliveryData(req, enableDelivery));

        // 取近 13 周对应的前N周的预测版本，对于跨周的 PPL 平均到周
        Future<List<DwsDemandWeekNPplVersionItemDO>> pplFuture = threadPool.submit(() -> getPplForecastWeekN(req));

        // 安全人工调整
        Future<Map<String, BigDecimal>> manualFuture = threadPool.submit(() -> SpringUtil.getBean(ManualConfigServiceImpl.class).querySnapshotManual(statTime));

        // 周转库存
        Future<Map<String, List<DwsInventoryHealthMckTurnoverWfAnyDO>>> turnoverFuture = threadPool.submit(() -> getTurnoverInventoryWeek13AvgV2(req));

        Future<Map<String, DwsInventoryHealthMckTurnoverWfDO>> turnoverCurFuture = threadPool.submit(() -> getTurnoverInventoryCurWeek(req));

        // 预测周转库存
        Future<List<DwsInventoryHealthMckForecastTurnoverDfAnyDO>> forecastTurnFuture = threadPool.submit(() -> getForecastTurnoverInventory(req));

        // 预测安全库存
        Future<List<DwsInventoryHealthMckForecastMonthlySafeDfAnyDO>> forecastSafeFuture = threadPool.submit(() -> getForecastMonthlySafeInventory(req));
        //获取历史周峰数据
        Future<List<DwsInventoryHealthWeeklyScaleDfAnyDO>> scaleMonFuture = threadPool.submit(() -> getInventoryHealthWeeklyScales(newReq));
        //获取供应波动
        Future<Map<String, List<DwsInventoryHealthWeeklyDeliveryDfDO>>> deliveryMonFuture =threadPool.submit(() -> getDeliveryData(newReq, enableDelivery));

        // 取近 13 周对应的前N周的预测版本，对于跨周的 PPL 平均到周
        Future<List<DwsDemandWeekNPplVersionItemDO>> pplMonFuture = threadPool.submit(() -> getPplForecastWeekN(newReq));

        //2.获取数据
        List<DwsInventoryHealthWeeklyScaleDfAnyDO> all;
        Map<String, List<DwsInventoryHealthWeeklyDeliveryDfDO>> deliveryDfMap;
        List<DwsDemandWeekNPplVersionItemDO> pplForecastWeekN;
        Map<String, List<DwsInventoryHealthMckTurnoverWfAnyDO>> turnoverDfDOMap;
        Map<String, DwsInventoryHealthMckTurnoverWfDO> turnoverCurWeekDOMap;
        List<DwsInventoryHealthMckForecastTurnoverDfAnyDO> forecastTurnoverDfDOS;
        List<DwsInventoryHealthMckForecastMonthlySafeDfAnyDO> forecastMonthlySafeDfDOS;
        Map<String, BigDecimal> manualConfigMap;
        List<DwsInventoryHealthWeeklyScaleDfAnyDO> allMon;
        Map<String, List<DwsInventoryHealthWeeklyDeliveryDfDO>> deliveryDfMonMap;
        List<DwsDemandWeekNPplVersionItemDO> pplForecastWeekNMon;
        try {
            all = scaleFuture.get();
            deliveryDfMap = deliveryFuture.get();
            pplForecastWeekN = pplFuture.get();
            turnoverDfDOMap = turnoverFuture.get();
            turnoverCurWeekDOMap = turnoverCurFuture.get();
            forecastTurnoverDfDOS = forecastTurnFuture.get();
            forecastMonthlySafeDfDOS = forecastSafeFuture.get();
            manualConfigMap = manualFuture.get();
            allMon = scaleMonFuture.get();
            deliveryDfMonMap = deliveryMonFuture.get();
            pplForecastWeekNMon = pplMonFuture.get();
        } catch (ExecutionException|InterruptedException e) {
            log.error("buildHistoryWeekPeakDemand方法内数据获取失败, message :" + e.getMessage());
            throw BizException.makeThrow("buildHistoryWeekPeakDemand方法内数据获取失败, message :" + e.getMessage());
        }

        // 获取 sla(国内和海外)
        Map<String, Integer> mainSla = operationViewService.getAllSLA(true); // 国内
        Map<String, Integer> noMainSla = operationViewService.getAllSLA(false); // 海外

        // 目标服务水平
        InventoryHealthConfigService inventoryHealthConfigService = SpringUtil.getBean(InventoryHealthConfigServiceImpl.class);
        Map<String, BigDecimal> resultMap = inventoryHealthConfigService.queryTargetServiceLevel(statTime);
        Map<String, BigDecimal> resultMonMap = inventoryHealthConfigService.queryTargetServiceLevel(monday);
        Map<String, InventoryHealthMainZoneNameConfigDO> zoneNameTypeMap = inventoryHealthConfigService.getZoneNameToTypeConfigMap(statTime);
        Map<String, InventoryHealthMainZoneNameConfigDO> zoneNameToTypeMonMap = inventoryHealthConfigService.getZoneNameToTypeConfigMap(
                monday);
        Map<String, String> instanceTypeType2map = inventoryHealthConfigService.getInstanceTypeToType2ConfigMap(statTime);
        Map<String, String> instanceTypeType2MonMap = inventoryHealthConfigService.getInstanceTypeToType2ConfigMap(
                monday);


        //3.数据处理
        List<OperationViewResp2.Item> items = resp.getData();
        Map<String, List<OperationViewResp2.Item>> itemMap = ListUtils.toMapList(items, o -> o.toKey(), o -> o);
        Map<String, BigDecimal> bufferMap = ListUtils.toMap(items, o -> o.toKey(), o -> o.getBufferSafetyInv());
        stopWatch.stop();

// 合并历史周峰数据以及预测数据
        stopWatch.start("合并历史周峰数据以及预测数据");
        List<HistoryWeekPeakDemandItemDTO> mergedItems = mergeWeekPeekDemand(all, pplForecastWeekN);
        List<HistoryWeekPeakDemandItemDTO> mergedItemsMon = mergeWeekPeekDemand(allMon,
                pplForecastWeekNMon);
        stopWatch.stop();

        boolean isTurnoverInvEmpty = ListUtils.isEmpty(turnoverCurWeekDOMap);

        Map<String, List<DwsInventoryHealthMckForecastTurnoverDfAnyDO>> forecastTurnoverMap = ListUtils.toMapList(
                forecastTurnoverDfDOS,
                item -> StringTools.join("@", item.getProductType(), item.getZoneName(), item.getInstanceType()),
                o -> o
        );

        Map<String, List<DwsInventoryHealthMckForecastMonthlySafeDfAnyDO>> forecastMonthlySafeMap = ListUtils.toMapList(
                forecastMonthlySafeDfDOS,
                item -> StringTools.join("@", item.getProductType(), item.getZoneName(), item.getInstanceType()),
                o -> o
        );

        Map<String, List<HistoryWeekPeakDemandItemDTO>> mapList = ListUtils.toMapList(
                mergedItems,
                item -> StringTools.join("@", item.getProductType(), item.getCustomhouseTitle(), item.getAreaName(), item.getRegionName(), item.getZoneName(), item.getInstanceType()),
                o -> o
        );
        Map<String, List<HistoryWeekPeakDemandItemDTO>> mapMonList = ListUtils.toMapList(
                mergedItemsMon,
                item -> StringTools.join("@", item.getProductType(), item.getCustomhouseTitle(), item.getAreaName(), item.getRegionName(), item.getZoneName(), item.getInstanceType()),
                o -> o
        );
        stopWatch.start("遍历集合数据计算指标");
        mergeAllIndex(resp, mapList, mapMonList, itemMap, bufferMap, mainSla, noMainSla, deliveryDfMap, deliveryDfMonMap, manualConfigMap, resultMap,resultMonMap, zoneNameTypeMap, zoneNameToTypeMonMap, instanceTypeType2map, instanceTypeType2MonMap,
                turnoverDfDOMap, turnoverCurWeekDOMap, forecastTurnoverMap, forecastMonthlySafeMap, enableDelivery, isTurnoverInvEmpty);
        stopWatch.stop();
        log.info(stopWatch.prettyPrint());
    }

    public List<HistoryWeekPeakDemandItemDTO> mergeWeekPeekDemand(List<DwsInventoryHealthWeeklyScaleDfAnyDO> all, List<DwsDemandWeekNPplVersionItemDO> pplForecastWeekN) {
        return ListUtils.merge(all, pplForecastWeekN,
                o1 -> StringTools.join("@", o1.getProductType(), o1.getHolidayWeekStartDate(), o1.getHolidayWeekEndDate(), o1.getCustomhouseTitle(), o1.getAreaName(), o1.getRegionName(), o1.getZoneName(), o1.getInstanceType()),
                o2 -> StringTools.join("@", o2.getProductType(), o2.getWeekStartDate(), o2.getWeekEndDate(), o2.getCustomhouseTitle(), o2.getAreaName(), o2.getRegionName(), o2.getZoneName(), o2.getInstanceType()),
                (o1, o2) -> {
                    HistoryWeekPeakDemandItemDTO dto = new HistoryWeekPeakDemandItemDTO();
                    if (ListUtils.isNotEmpty(o1)) {
                        dto.setCustomhouseTitle(o1.get(0).getCustomhouseTitle());
                        dto.setAreaName(o1.get(0).getAreaName());
                        dto.setRegionName(o1.get(0).getRegionName());
                        dto.setZoneName(o1.get(0).getZoneName());
                        dto.setInstanceType(o1.get(0).getInstanceType());
                        dto.setWeekStartDate(o1.get(0).getHolidayWeekStartDate().toString());
                        dto.setWeekEndDate(o1.get(0).getHolidayWeekEndDate().toString());
                        dto.setWeekIndex(o1.get(0).getWeekIndex());
                        dto.setProductType(o1.get(0).getProductType());
                    } else {
                        dto.setCustomhouseTitle(o2.get(0).getCustomhouseTitle());
                        dto.setAreaName(o2.get(0).getAreaName());
                        dto.setRegionName(o2.get(0).getRegionName());
                        dto.setZoneName(o2.get(0).getZoneName());
                        dto.setInstanceType(o2.get(0).getInstanceType());
                        dto.setWeekStartDate(o2.get(0).getWeekStartDate());
                        dto.setWeekEndDate(o2.get(0).getWeekEndDate());
                        dto.setWeekIndex(o2.get(0).getWeekIndex());
                        dto.setProductType(o2.get(0).getProductType());
                    }

                    BigDecimal totalCore = NumberUtils.sum(o2, o -> o.getTotalCore());
                    BigDecimal averageTotalCore = NumberUtils.sum(o2, o -> o.getAverageTotalCore());
                    BigDecimal weekPeakNum = NumberUtils.sum(o1, o -> o.getWeekPeakLogicNum());

                    dto.setTotalCore(totalCore == null ? BigDecimal.ZERO : totalCore);
                    dto.setAverageTotalCore(averageTotalCore == null ? BigDecimal.ZERO : averageTotalCore);
                    dto.setWeekPeakNum(weekPeakNum == null ? BigDecimal.ONE : weekPeakNum);
                    return dto;
                }
        );
    }


    public void mergeAllIndex(OperationViewResp2 resp,
            Map<String, List<HistoryWeekPeakDemandItemDTO>> mapList,
            Map<String, List<HistoryWeekPeakDemandItemDTO>> mapMonList,
            Map<String, List<OperationViewResp2.Item>> itemMap,
            Map<String, BigDecimal> bufferMap,
            Map<String, Integer> mainSla,
            Map<String, Integer> noMainSla,
            Map<String, List<DwsInventoryHealthWeeklyDeliveryDfDO>> deliveryDfMap,
            Map<String, List<DwsInventoryHealthWeeklyDeliveryDfDO>> deliveryDfMonMap,
            Map<String, BigDecimal> manualConfigMap,
            Map<String, BigDecimal> resultMap,
            Map<String, BigDecimal> resultMonMap,
            Map<String, InventoryHealthMainZoneNameConfigDO> zoneNameTypeMap,
            Map<String, InventoryHealthMainZoneNameConfigDO> zoneNameToTypeMonMap,
            Map<String, String> instanceTypeType2map,
            Map<String, String> instanceTypeType2MonMap,
            Map<String, List<DwsInventoryHealthMckTurnoverWfAnyDO>> turnoverDfDOMap,
            Map<String, DwsInventoryHealthMckTurnoverWfDO> turnoverCurWeekDOMap,
            Map<String, List<DwsInventoryHealthMckForecastTurnoverDfAnyDO>> forecastTurnoverMap,
            Map<String, List<DwsInventoryHealthMckForecastMonthlySafeDfAnyDO>> forecastMonthlySafeMap,
            boolean enableDelivery,
            boolean isTurnoverInvEmpty) {
        List<OperationViewResp2.Item> itemBatch = Lang.list();
        for (Map.Entry<String, List<HistoryWeekPeakDemandItemDTO>> e : mapList.entrySet()) {
            OperationViewResp2.Item item = new OperationViewResp2.Item();
            item.setProductType(e.getValue().get(0).getProductType());
            item.setCustomhouseTitle(e.getValue().get(0).getCustomhouseTitle());
            item.setAreaName(e.getValue().get(0).getAreaName());
            item.setRegionName(e.getValue().get(0).getRegionName());
            item.setZoneName(e.getValue().get(0).getZoneName());
            item.setInstanceType(e.getValue().get(0).getInstanceType());
            item.setInvTotalNum(NumberUtils.sum(itemMap.get(e.getKey()), o -> o.getInvTotalNum()));
            item.setBufferSafetyInv(bufferMap.get(e.getKey()));

            OperationViewResp2.SafetyInventoryResult historyWeekPeak = new OperationViewResp2.SafetyInventoryResult();
            item.setHistoryWeekPeakForecastWN(historyWeekPeak);

            boolean isMainLand = Objects.equals(item.getCustomhouseTitle(), "境内");
            Integer slaV = isMainLand? mainSla.get(item.getInstanceType()) : noMainSla.get(item.getInstanceType());
            if (slaV == null){
                slaV = operationViewService.getDefSLA(isMainLand);
            }

            //  交付SLA
            BigDecimal sla = BigDecimal.valueOf(slaV);
            historyWeekPeak.setSla(sla.intValue());

            // 13周交付周期
            setDeliveryData(historyWeekPeak, deliveryDfMap, e.getKey());
            List<BigDecimal> deliveryData = calcDeliveryData(deliveryDfMonMap, e.getKey());
            BigDecimal deliveryAvg;
            BigDecimal deliveryStandardMonDiff;
            if (deliveryData != null) {
                deliveryAvg = deliveryData.get(0);
                deliveryStandardMonDiff = deliveryData.get(1);
            } else {
                deliveryAvg = BigDecimal.valueOf(historyWeekPeak.getSla());
                deliveryStandardMonDiff = null;
            }


            // 安全库存人工调整
            String manualConfigKey = Strings.join("@", item.getZoneName(), item.getInstanceType());
            historyWeekPeak.setSafeInvManualConfig(manualConfigMap.getOrDefault(manualConfigKey, BigDecimal.ZERO));

            historyWeekPeak.setAlgorithm("MCK历史&预测需求");

            historyWeekPeak.setWeekDemandMap(ListUtils.toMap(e.getValue(), o -> "w" + (o.getWeekIndex()), o ->
                    o.getWeekPeakNum().setScale(2, BigDecimal.ROUND_HALF_UP)));
            historyWeekPeak.setWeekNForecastMap(ListUtils.toMap(e.getValue(), o -> "w" + (o.getWeekIndex()), o ->
                    o.getAverageTotalCore().setScale(2, BigDecimal.ROUND_HALF_UP)));

            fillWeekDemandMap(historyWeekPeak.getWeekDemandMap());
            fillWeekDemandMap(historyWeekPeak.getWeekNForecastMap());

            String tempKey = e.getKey();
            Map<String, BigDecimal> weekDemandMonMap = ListUtils.toMap(mapMonList.get(tempKey), o -> "w" + (o.getWeekIndex()), o ->
                    o.getWeekPeakNum().setScale(2, BigDecimal.ROUND_HALF_UP));
            Map<String, BigDecimal> weekNForecastMonMap = ListUtils.toMap(mapMonList.get(tempKey), o -> "w" + (o.getWeekIndex()), o ->
                    o.getAverageTotalCore().setScale(2, BigDecimal.ROUND_HALF_UP));
            fillWeekDemandMap(weekDemandMonMap);
            fillWeekDemandMap(weekNForecastMonMap);

//            List<BigDecimal> demands = ListUtils.transform(e.getValue(), o -> o.getWeekPeakNum());
            List<BigDecimal> data1 = historyWeekPeak.getWeekDemandMap().entrySet().stream().map(w -> w.getValue()).collect(Collectors.toList());
            List<BigDecimal> data2 = historyWeekPeak.getWeekNForecastMap().entrySet().stream().map(w -> w.getValue()).collect(Collectors.toList());
            BigDecimal demandSD = OperationViewTools.calculateSD(data1, data2);
            historyWeekPeak.setStandardDiff(demandSD);

            List<BigDecimal> data3 = weekDemandMonMap.entrySet().stream().map(w -> w.getValue()).collect(Collectors.toList());
            List<BigDecimal> data4 = weekNForecastMonMap.entrySet().stream().map(w -> w.getValue()).collect(Collectors.toList());
            BigDecimal demandMonSD = OperationViewTools.calculateSD(data3, data4);

            //  服务水平
            BigDecimal serviceLevel = this.getTargetServiceLevel(
                    resultMap,
                    zoneNameTypeMap,
                    instanceTypeType2map,
                    item.getZoneName(), item.getInstanceType(), "包年包月");
            //用于计算安全库存的服务水平
            BigDecimal serviceMonLevel = this.getTargetServiceLevel(
                    resultMonMap,
                    zoneNameToTypeMonMap,
                    instanceTypeType2MonMap,
                    item.getZoneName(), item.getInstanceType(), "包年包月");
            historyWeekPeak.setServiceLevel(serviceLevel);
            //  服务系数
            double serviceNum = OperationViewTools.normsinv(serviceLevel.doubleValue());
            historyWeekPeak.setServiceLevelFactor(BigDecimal.valueOf(serviceNum));

            //  用于计算安全库存的服务系数
            double serviceMonNum = OperationViewTools.normsinv(serviceMonLevel.doubleValue());

            //  弹性服务水平
            BigDecimal bufferServiceLevel = this.getTargetServiceLevel(
                    resultMap,
                    zoneNameTypeMap,
                    instanceTypeType2map,
                    item.getZoneName(), item.getInstanceType(), "弹性");
            historyWeekPeak.setBufferServiceLevel(bufferServiceLevel);

            //  弹性服务系数
            double bufferServiceNum =
                    OperationViewTools.normsinv(historyWeekPeak.getBufferServiceLevel().doubleValue());
            historyWeekPeak.setBufferServiceLevelFactor(BigDecimal.valueOf(bufferServiceNum));

            BigDecimal avgDemandNum = NumberUtils.avg(historyWeekPeak.getWeekDemandMap().values(), 6);

            BigDecimal avgDemandMonNum = NumberUtils.avg(weekDemandMonMap.values(), 6);

            //  包月安全库存
            BigDecimal finalSla = deliveryAvg != null ? deliveryAvg : sla;
            BigDecimal deliveryStandardDiff = null;
            if (enableDelivery) {
                deliveryStandardDiff = deliveryStandardMonDiff != null ? deliveryStandardMonDiff : BigDecimal.ZERO;
            }
            BigDecimal monthlyInvNum = calMonthlyInvNum(serviceMonNum, finalSla, demandMonSD, avgDemandMonNum, deliveryStandardDiff);
            historyWeekPeak.setMonthlySafetyInv(monthlyInvNum);

            //  安全库存 = 包月安全库存 + 弹性备货配额 + 人工调整
            historyWeekPeak.setSafetyInv(NumberUtils.max(AmountUtils.add(historyWeekPeak.getMonthlySafetyInv(),
                    item.getBufferSafetyInv() != null ? item.getBufferSafetyInv() : BigDecimal.ZERO).add(historyWeekPeak.getSafeInvManualConfig()), BigDecimal.ZERO));

            historyWeekPeak.setDemandAvg(avgDemandNum);

//            BigDecimal invCacheNum = avgDemandNum.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : avgDemandNum;
            // 当周的周转库存有值，则设置，否则始终为 null
            // 计算 13 周转库存均值
            List<DwsInventoryHealthMckTurnoverWfAnyDO> mckTurnoverDfDOS = turnoverDfDOMap.get(StringTools.join("@", item.getProductType(), item.getZoneName(), item.getInstanceType()));
            historyWeekPeak.setTurnoverInv(NumberUtils.avg(mckTurnoverDfDOS, 6, o -> o.getTurnoverInv()));
            historyWeekPeak.setTurnoverInvWeekPeak(NumberUtils.avg(mckTurnoverDfDOS, 6, o -> o.getWeekPeakCore()));
            historyWeekPeak.setTurnoverInvReserved(NumberUtils.avg(mckTurnoverDfDOS, 6, o -> o.getWeekReservedAvgCore()));

            if (!isTurnoverInvEmpty) {
                DwsInventoryHealthMckTurnoverWfDO mckTurnoverWfDO = turnoverCurWeekDOMap.get(StringTools.join("@", item.getProductType(), item.getZoneName(), item.getInstanceType()));
                if (mckTurnoverWfDO != null) {
                    historyWeekPeak.setTurnoverInvCurWeek(mckTurnoverWfDO.getTurnoverInv());
                    historyWeekPeak.setTurnoverInvCurWeekPeak(mckTurnoverWfDO.getWeekPeakCore());
                    historyWeekPeak.setTurnoverInvCurWeekAvgReserved(mckTurnoverWfDO.getWeekReservedAvgCore());
                }
            }



            // 预测周转库存
            List<DwsInventoryHealthMckForecastTurnoverDfAnyDO> forecastTurnoverDfDOList = forecastTurnoverMap.get(StringTools.join("@", item.getProductType(), item.getZoneName(), item.getInstanceType()));
            historyWeekPeak.setForecastTurnOverInvMap(ListUtils.toMap(forecastTurnoverDfDOList, o -> "w" + (o.getWeekIndex()), o -> {
                Map<String, BigDecimal> map = new HashMap<>();
                map.put("turnoverInv", o.getTurnoverInv());
                map.put("weekPeakCore", o.getWeekPeakCore());
                map.put("avgForecastCore", o.getAvgForecastCore());
                map.put("totalForecastCore", o.getTotalForecastCore());
                return map;
            }));


            // 预测包月安全库存
            List<DwsInventoryHealthMckForecastMonthlySafeDfAnyDO> forecastMonthlySafeDfDOList = forecastMonthlySafeMap.get(StringTools.join("@", item.getProductType(), item.getZoneName(), item.getInstanceType()));
            if (ListUtils.isNotEmpty(forecastMonthlySafeDfDOList))  {
                historyWeekPeak.setTurnoverWeekNum(enableDelivery ? forecastMonthlySafeDfDOList.get(0).getTurnoverWeekNum() : forecastMonthlySafeDfDOList.get(0).getNoDeliveryTurnoverWeekNum());
                historyWeekPeak.setHistorySafeInvAvg13(enableDelivery ? forecastMonthlySafeDfDOList.get(0).getSafeInvAvg13() : forecastMonthlySafeDfDOList.get(0).getNoDeliverySafeInvAvg13());
            }
            historyWeekPeak.setForecastMonthSafetyInvMap(ListUtils.toMap(forecastMonthlySafeDfDOList, o -> "w" + (o.getWeekIndex()), o -> {
                Map<String, BigDecimal> map = new HashMap<>();
                map.put("turnoverInv", o.getTurnoverInv());
                map.put("monthlySafeInv", enableDelivery ? o.getMonthlySafeInv() : o.getNoDeliveryMonthlySafeInv());
                map.put("weekPeakAvg13", o.getWeekPeakAvg13());
                map.put("safeInvAvg13", enableDelivery ? o.getSafeInvAvg13() : o.getNoDeliverySafeInvAvg13());
                map.put("turnoverWeekNum", enableDelivery ? o.getTurnoverWeekNum() : o.getNoDeliveryTurnoverWeekNum());
                return map;
            }));


            // 预测安全库存
            historyWeekPeak.setForecastSafetyInvMap(ListUtils.toMap(forecastMonthlySafeDfDOList, o -> "w" + (o.getWeekIndex()), o -> {
                BigDecimal safeInv = NumberUtils.max(AmountUtils.add(o.getMonthlySafeInv(),
                        item.getBufferSafetyInv() != null ? item.getBufferSafetyInv() : BigDecimal.ZERO), BigDecimal.ZERO);
                return safeInv;
            }));
            itemBatch.add(item);
        }
        resp.putHistoryWeekPeakForecastWNToItem(itemBatch);
    }


    public boolean isDeliveryEnabledForAlgorithm() {
        String status = redisHelper.getString("operationViewEnableDeliveryDataForAlgorithm");

        if (status == null) {
            return false;
        }

        return status.equals("生效");
    }

    public void buildHistoryWeekDiff(OperationViewReq2 req, OperationViewResp2 resp) {
        String statTime = DateUtils.formatDate(req.getDate());
        LocalDate date = LocalDate.parse(statTime);
        String monday = DateUtils.formatDate(date.with(DayOfWeek.MONDAY));
        String customerCustomGroup = req.getCustomerCustomGroup();
        CustomerCustomGroupEnum groupEnum = CustomerCustomGroupEnum.getByCode(customerCustomGroup);
        if (groupEnum == null) {
            groupEnum = CustomerCustomGroupEnum.ALL;
        }

        WhereSQL condition = req.genCondition();
        condition.and("week_index < 0 and product_type = 'CVM'");
        condition.and("customer_custom_group = ?", groupEnum.getName());
        if (ListUtils.isNotEmpty(req.getExcludeUinList())) {
            condition.and("exclude_uin_list = ?", req.handleExcludeUinList());
        } else {
            condition.and("exclude_uin_list = '(空值)'");
        }
        WhereSQL monCondition = condition.copy();
        condition.and("stat_time = ?", statTime);
        monCondition.and("stat_time = ?", monday);

        WhereSQL categoryCondition = genCategoryCondition(req.getZoneCategory(), req.getInstanceTypeCategory(), req.getIsCombine(), req.getCategoryDate(), req.getCustomhouseTitle());
        condition.and(categoryCondition);

        List<DwsInventoryHealthWeeklyScaleDfDO> all = ckcldDBHelper.getAll(
                DwsInventoryHealthWeeklyScaleDfDO.class, condition.getSQL(), condition.getParams());
        List<DwsInventoryHealthWeeklyScaleDfDO> allMon = ckcldDBHelper.getAll(
                DwsInventoryHealthWeeklyScaleDfDO.class, monCondition.getSQL(), monCondition.getParams());

        Map<String, List<DwsInventoryHealthWeeklyScaleDfDO>> mapList = ListUtils.toMapList(all, o -> toKey(o), o -> o);
        Map<String, List<DwsInventoryHealthWeeklyScaleDfDO>> mapMonList = ListUtils.toMapList(allMon, o -> toKey(o), o -> o);

        boolean enableDelivery = isDeliveryEnabledForAlgorithm();
        OperationViewReq2 newReq = createNewReq(req, monday);
        Map<String, List<DwsInventoryHealthWeeklyDeliveryDfDO>> deliveryDfMap = getDeliveryData(req, enableDelivery);
        Map<String, List<DwsInventoryHealthWeeklyDeliveryDfDO>> deliveryDfMonMap = getDeliveryData(newReq, enableDelivery);

        List<OperationViewResp2.Item> items = resp.getData();
        Map<String, List<OperationViewResp2.Item>> itemMap = ListUtils.toMapList(items, o -> o.toKey(), o -> o);
//
        Map<String, BigDecimal> bufferMap = ListUtils.toMap(items, o -> o.toKey(), o -> o.getBufferSafetyInv());

        // 安全人工调整
        Map<String, BigDecimal> manualConfigMap =
                SpringUtil.getBean(ManualConfigServiceImpl.class).querySnapshotManual(statTime);
        List<OperationViewResp2.Item> itemBatch = Lang.list();

        // 周转库存
        Map<String, List<DwsInventoryHealthMckTurnoverWfDO>> turnoverDfDOMap = getTurnoverInventoryWeek13Avg(req);
        Map<String, DwsInventoryHealthMckTurnoverWfDO> turnoverCurWeekDOMap = getTurnoverInventoryCurWeek(req);
        boolean isTurnoverInvEmpty = ListUtils.isEmpty(turnoverCurWeekDOMap);

        // 预测周转库存
        List<DwsInventoryHealthMckForecastTurnoverDfAnyDO> forecastTurnoverDfDOS = getForecastTurnoverInventory(req);
        Map<String, List<DwsInventoryHealthMckForecastTurnoverDfAnyDO>> forecastTurnoverMap = ListUtils.toMapList(
                forecastTurnoverDfDOS,
                item -> StringTools.join("@", item.getProductType(), item.getZoneName(), item.getInstanceType()),
                o -> o
        );

        for (Map.Entry<String, List<DwsInventoryHealthWeeklyScaleDfDO>> e : mapList.entrySet()) {
            OperationViewResp2.Item item = new OperationViewResp2.Item();
            item.setProductType(e.getValue().get(0).getProductType());
            item.setCustomhouseTitle(e.getValue().get(0).getCustomhouseTitle());
            item.setAreaName(e.getValue().get(0).getAreaName());
            item.setRegionName(e.getValue().get(0).getRegionName());
            item.setZoneName(e.getValue().get(0).getZoneName());
            item.setInstanceType(e.getValue().get(0).getInstanceType());
            item.setInvTotalNum(NumberUtils.sum(itemMap.get(e.getKey()), o -> o.getInvTotalNum()));
            item.setBufferSafetyInv(bufferMap.get(e.getKey()));


            OperationViewResp2.SafetyInventoryResult historyWeekDiff = new OperationViewResp2.SafetyInventoryResult();
            item.setHistoryWeekDiff(historyWeekDiff);

            //  交付SLA
            BigDecimal sla = BigDecimal.valueOf(operationViewService.getSLAByInstanceType(item.getInstanceType(),
                    Objects.equals(item.getCustomhouseTitle(), "境内")));
            historyWeekDiff.setSla(sla.intValue());

            // 13周交付周期
            setDeliveryData(historyWeekDiff, deliveryDfMap, e.getKey());

            List<BigDecimal> deliveryData = calcDeliveryData(deliveryDfMonMap, e.getKey());
            BigDecimal deliveryAvg;
            BigDecimal deliveryStandardMonDiff;
            if (deliveryData != null) {
                deliveryAvg = deliveryData.get(0);
                deliveryStandardMonDiff = deliveryData.get(1);
            } else {
                deliveryAvg = BigDecimal.valueOf(historyWeekDiff.getSla());
                deliveryStandardMonDiff = null;
            }

            // 安全库存人工调整
            String manualConfigKey = Strings.join("@", item.getZoneName(), item.getInstanceType());
            historyWeekDiff.setSafeInvManualConfig(manualConfigMap.getOrDefault(manualConfigKey, BigDecimal.ZERO));
            historyWeekDiff.setAlgorithm("历史周需求");
            historyWeekDiff.setWeekDemandMap(ListUtils.toMap(e.getValue(), o -> "w" + (o.getWeekIndex()),
                    o -> o.getWeekDiffLogicNum().setScale(2, RoundingMode.HALF_UP)));
            fillWeekDemandMap(historyWeekDiff.getWeekDemandMap());
            List<BigDecimal> demands = ListUtils.transform(e.getValue(), o -> o.getWeekDiffLogicNum());
            BigDecimal demandSD = OperationViewTools.calculateSD(demands);
            historyWeekDiff.setStandardDiff(demandSD);
            List<BigDecimal> monDemands = ListUtils.transform(mapMonList.get(e.getKey()), o -> o.getWeekDiffLogicNum());
            BigDecimal demandMonSD = OperationViewTools.calculateSD(monDemands);

            //  服务水平
            BigDecimal serviceLevel = this.getTargetServiceLevel(statTime, item.getZoneName(), item.getInstanceType(), "包年包月");
            historyWeekDiff.setServiceLevel(serviceLevel);

            BigDecimal serviceMonLevel = this.getTargetServiceLevel(monday, item.getZoneName(), item.getInstanceType(), "包年包月");

            //  服务系数
            double serviceNum = OperationViewTools.normsinv(serviceLevel.doubleValue());
            double serviceMonNum = OperationViewTools.normsinv(serviceMonLevel.doubleValue());
            historyWeekDiff.setServiceLevelFactor(BigDecimal.valueOf(serviceNum));

            //  弹性服务水平
            BigDecimal bufferServiceLevel = this.getTargetServiceLevel(statTime, item.getZoneName(), item.getInstanceType(), "弹性");
            historyWeekDiff.setBufferServiceLevel(bufferServiceLevel);

            //  弹性服务系数
            double bufferServiceNum =
                    OperationViewTools.normsinv(historyWeekDiff.getBufferServiceLevel().doubleValue());
            historyWeekDiff.setBufferServiceLevelFactor(BigDecimal.valueOf(bufferServiceNum));

            BigDecimal avgDemandNum = NumberUtils.avg(demands, 6);
            BigDecimal avgDemandMonNum = NumberUtils.avg(monDemands, 6);
            //  包月安全库存
            // SLA 取真实 SLA（若存在）或者计划的 SLA
            BigDecimal finalSla = deliveryAvg != null ? deliveryAvg : sla;
            BigDecimal deliveryStardardDiff = null;
            if (enableDelivery) {
                deliveryStardardDiff = deliveryStandardMonDiff != null ? deliveryStandardMonDiff : BigDecimal.ZERO;
            }
            BigDecimal monthlyInvNum = calMonthlyInvNum(serviceMonNum, finalSla, demandMonSD, avgDemandMonNum, deliveryStardardDiff);
            historyWeekDiff.setMonthlySafetyInv(monthlyInvNum);

            //  安全库存 = 包月安全库存 + 弹性备货配额 + 人工调整
            historyWeekDiff.setSafetyInv(NumberUtils.max(AmountUtils.add(historyWeekDiff.getMonthlySafetyInv(),
                    item.getBufferSafetyInv() != null ? item.getBufferSafetyInv() : BigDecimal.ZERO).add(historyWeekDiff.getSafeInvManualConfig()), BigDecimal.ZERO));

            //  周转库存
//            BigDecimal invCacheNum = avgDemandNum.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : avgDemandNum;
            historyWeekDiff.setDemandAvg(avgDemandNum);
//            historyWeekDiff.setTurnoverInv(invCacheNum);
            List<DwsInventoryHealthMckTurnoverWfDO> mckTurnoverDfDOS = turnoverDfDOMap.get(StringTools.join("@", item.getProductType(), item.getZoneName(), item.getInstanceType()));
            historyWeekDiff.setTurnoverInv(NumberUtils.avg(mckTurnoverDfDOS, 6, o -> o.getTurnoverInv()));
            historyWeekDiff.setTurnoverInvWeekPeak(NumberUtils.avg(mckTurnoverDfDOS, 6, o -> o.getWeekPeakCore()));
            historyWeekDiff.setTurnoverInvReserved(NumberUtils.avg(mckTurnoverDfDOS, 6, o -> o.getWeekReservedAvgCore()));

            if (!isTurnoverInvEmpty) {
                DwsInventoryHealthMckTurnoverWfDO mckTurnoverWfDO = turnoverCurWeekDOMap.get(StringTools.join("@", item.getProductType(), item.getZoneName(), item.getInstanceType()));
                if (mckTurnoverWfDO != null) {
                    historyWeekDiff.setTurnoverInvCurWeek(mckTurnoverWfDO.getTurnoverInv());
                    historyWeekDiff.setTurnoverInvCurWeekPeak(mckTurnoverWfDO.getWeekPeakCore());
                    historyWeekDiff.setTurnoverInvCurWeekAvgReserved(mckTurnoverWfDO.getWeekReservedAvgCore());
                }
            }

            // 预测周转库存
            List<DwsInventoryHealthMckForecastTurnoverDfAnyDO> forecastTurnoverDfDOList = forecastTurnoverMap.get(StringTools.join("@", item.getProductType(), item.getZoneName(), item.getInstanceType()));
            historyWeekDiff.setForecastTurnOverInvMap(ListUtils.toMap(forecastTurnoverDfDOList, o -> "w" + (o.getWeekIndex()), o -> {
                Map<String, BigDecimal> map = new HashMap<>();
                map.put("turnoverInv", o.getTurnoverInv());
                map.put("weekPeakCore", o.getWeekPeakCore());
                map.put("avgForecastCore", o.getAvgForecastCore());
                map.put("totalForecastCore", o.getTotalForecastCore());
                return map;
            }));

            itemBatch.add(item);
        }
        resp.putHistoryWeekDiffToItem(itemBatch);
    }

    /**
     * 构建未来周峰部分数据
     */
    public void buildFutureWeekPeak(OperationViewReq2 req, OperationViewResp2 resp) {
        String statTime = DateUtils.formatDate(req.getDate());
        String customerCustomGroup = req.getCustomerCustomGroup();
        List<CustomerCustomGroupEnum> groupEnum = Lang.list();

        if (StringTools.isBlank(customerCustomGroup)) {
            groupEnum.add(CustomerCustomGroupEnum.MEDIUM_LONG_TAIL);
        } else {
            switch (customerCustomGroup) {
                case "ALL":
                    groupEnum.addAll(Lang.list(CustomerCustomGroupEnum.MEDIUM_LONG_TAIL,
                            CustomerCustomGroupEnum.HEAD_ZLKHB, CustomerCustomGroupEnum.HEAD_NOT_ZLKHB));
                    break;
                case "LIST_REPORT":
                    groupEnum.addAll(Lang.list(CustomerCustomGroupEnum.HEAD_ZLKHB, CustomerCustomGroupEnum.HEAD_NOT_ZLKHB));
                    break;
                case "MEDIUM_LONG_TAIL":
                    groupEnum.add(CustomerCustomGroupEnum.MEDIUM_LONG_TAIL);
                    break;
            }
        }

        WhereSQL condition = req.genCondition();
        condition.and("stat_time = ?", statTime);
        if (req.getIsForecast() != null && req.getIsForecast()) {
            condition.and("week_index >= 0 and product_type = 'CVM'");
        } else {
            condition.and("week_index = 0 and product_type = 'CVM'");
        }
        condition.and("customer_custom_group in (?)", ListUtils.transform(groupEnum, o -> o.getName()));
        condition.and("exclude_uin_list = '(空值)'");

        WhereSQL categoryCondition = genCategoryCondition(req.getZoneCategory(), req.getInstanceTypeCategory(), req.getIsCombine(), req.getCategoryDate(), req.getCustomhouseTitle());
        condition.and(categoryCondition);

        List<DwsInventoryHealthWeeklyScaleDfDO> all = ckcldDBHelper.getAll(
                DwsInventoryHealthWeeklyScaleDfDO.class, condition.getSQL(), condition.getParams());

        //  过滤出当前周的安全库存明细
        List<DwsInventoryHealthWeeklyScaleDfDO> curWeek =
                ListUtils.filter(all, o -> Objects.equals(o.getWeekIndex(), 0));
        Map<String, List<DwsInventoryHealthWeeklyScaleDfDO>> curWeekMap =
                ListUtils.toMapList(curWeek, o -> toKey(o), o -> o);

        //  过滤出未来周的安全库存明细
        List<DwsInventoryHealthWeeklyScaleDfDO> futureWeeks =
                ListUtils.filter(all, o -> !Objects.equals(o.getWeekIndex(), 0));
        Map<String, List<DwsInventoryHealthWeeklyScaleDfDO>> futureWeeksMap =
                ListUtils.toMapList(futureWeeks, o -> toKey(o), o -> o);

        //  战略客户部部分的数据查出来就是安全库存的结果值
        List<DwsInventoryHealthWeeklyScaleDfDO> headZlkhbPartData = ListUtils.filter(all,
                o -> Objects.equals(o.getCustomerCustomGroup(), CustomerCustomGroupEnum.HEAD_ZLKHB.getName()));
        Map<String, BigDecimal> headZlkhbMap =
                ListUtils.toMap(headZlkhbPartData, o -> toKey(o), o -> o.getWeekPeakLogicNum());


        List<OperationViewResp2.Item> items = resp.getData();
        Map<String, List<OperationViewResp2.Item>> itemMap = ListUtils.toMapList(items, o -> o.toKey(), o -> o);
        Map<String, BigDecimal> bufferMap = ListUtils.toMap(items, o -> o.toKey(), o -> o.getBufferSafetyInv());

        // 安全人工调整
        Map<String, BigDecimal> manualConfigMap =
                SpringUtil.getBean(ManualConfigServiceImpl.class).querySnapshotManual(statTime);
        List<OperationViewResp2.Item> itemBatch = Lang.list();

        for (Map.Entry<String, List<DwsInventoryHealthWeeklyScaleDfDO>> e : curWeekMap.entrySet()) {
            OperationViewResp2.Item item = new OperationViewResp2.Item();
            item.setProductType(e.getValue().get(0).getProductType());
            item.setCustomhouseTitle(e.getValue().get(0).getCustomhouseTitle());
            item.setAreaName(e.getValue().get(0).getAreaName());
            item.setRegionName(e.getValue().get(0).getRegionName());
            item.setZoneName(e.getValue().get(0).getZoneName());
            item.setInstanceType(e.getValue().get(0).getInstanceType());
            item.setBufferSafetyInv(bufferMap.get(e.getKey()));
            item.setInvTotalNum(NumberUtils.sum(itemMap.get(e.getKey()), o -> o.getInvTotalNum()));

            OperationViewResp2.SafetyInventoryResult futureWeekPeak = new OperationViewResp2.SafetyInventoryResult();
            item.setFutureWeekPeak(futureWeekPeak);

            // 安全库存人工调整
            String manualConfigKey = Strings.join("@", item.getZoneName(), item.getInstanceType());
            futureWeekPeak.setSafeInvManualConfig(manualConfigMap.getOrDefault(manualConfigKey, BigDecimal.ZERO));
            futureWeekPeak.setAlgorithm("未来周需求");

            //  交付SLA
            int sla = BigDecimal.valueOf(operationViewService.getSLAByInstanceType(item.getInstanceType(),
                    Objects.equals(item.getCustomhouseTitle(), "境内"))).intValue();
            futureWeekPeak.setSla(sla);

            //  服务水平
            BigDecimal serviceLevel = this.getTargetServiceLevel(statTime, item.getZoneName(), item.getInstanceType(), "包年包月");
            futureWeekPeak.setServiceLevel(serviceLevel);

            //  服务系数
            double serviceNum = OperationViewTools.normsinv(serviceLevel.doubleValue());
            futureWeekPeak.setServiceLevelFactor(BigDecimal.valueOf(serviceNum));

            //  弹性服务水平
            BigDecimal bufferServiceLevel = this.getTargetServiceLevel(statTime, item.getZoneName(), item.getInstanceType(), "弹性");
            futureWeekPeak.setBufferServiceLevel(bufferServiceLevel);

            //  弹性服务系数
            double bufferServiceNum =
                    OperationViewTools.normsinv(futureWeekPeak.getBufferServiceLevel().doubleValue());
            futureWeekPeak.setBufferServiceLevelFactor(BigDecimal.valueOf(bufferServiceNum));

//            //  弹性核心数
//            List<BufferAverageCoreDTO> bufferAverageCoreDTOS =
//                    bufferMap.get(StringTools.join("@", item.getZoneName(), item.getInstanceType()));
//            if (bufferAverageCoreDTOS != null && bufferAverageCoreDTOS.size() > 0
//                    && bufferAverageCoreDTOS.get(0).getBufferAverageCore() != null) {
//                futureWeekPeak.setBufferAverageCore(bufferAverageCoreDTOS.get(0).getBufferAverageCore());
//            } else {
//                futureWeekPeak.setBufferAverageCore(0);
//            }
//
//            //  弹性备货配额
//            futureWeekPeak.setBufferSafetyInv(AmountUtils.multiply(
//                            BigDecimal.valueOf(futureWeekPeak.getBufferAverageCore()),
//                            SAFETY_INVENTORY_SERVICE_LEVEL_FOR_BUFFER.get()));

            //  自动化预测准确率
            futureWeekPeak.setForecastDemandAccuracyRate(SAFETY_INVENTORY_MAPE.get());

            //  需求预测量-中长尾
            BigDecimal mediumLongTailForecastDemandCore = NumberUtils.sum(ListUtils.filter(e.getValue(),
                            o -> Objects.equals(o.getCustomerCustomGroup(), CustomerCustomGroupEnum.MEDIUM_LONG_TAIL.getName())),
                    o -> o.getWeekPeakLogicNum());

            //  需求预测量-头部战略-没有周需求量
            BigDecimal headZlkhbForecastDemandCore = BigDecimal.ZERO;

            //  需求预测量-头部非战略
            BigDecimal headNotZlkhbForecastDemandCore = NumberUtils.sum(ListUtils.filter(e.getValue(),
                            o -> Objects.equals(o.getCustomerCustomGroup(), CustomerCustomGroupEnum.HEAD_NOT_ZLKHB.getName())),
                    o -> o.getWeekPeakLogicNum());

            BigDecimal forecast = AmountUtils.add(mediumLongTailForecastDemandCore,
                    headZlkhbForecastDemandCore, headNotZlkhbForecastDemandCore);

            if (forecast.compareTo(BigDecimal.ZERO) < 0) {
                forecast = BigDecimal.ZERO;
                mediumLongTailForecastDemandCore = BigDecimal.ZERO;
                headNotZlkhbForecastDemandCore = BigDecimal.ZERO;
            }

            //  未来周预测量D，当前周的预测量
            futureWeekPeak.setForecastDemandCore(forecast);
            futureWeekPeak.setMediumLongTailForecastDemandCore(mediumLongTailForecastDemandCore);
            futureWeekPeak.setHeadZlkhbForecastDemandCore(headZlkhbForecastDemandCore);
            futureWeekPeak.setHeadNotZlkhbForecastDemandCore(headNotZlkhbForecastDemandCore);

            //  包月安全库存-中长尾
            double mediumLongTailSafetyInv = futureWeekPeak.getServiceLevelFactor().doubleValue() *
                    futureWeekPeak.getMediumLongTailForecastDemandCore().doubleValue() *
                    (1 - SAFETY_INVENTORY_MAPE.get().doubleValue()) * Math.sqrt(sla / 7.0);
            futureWeekPeak.setMediumLongTailSafetyInv(BigDecimal.valueOf(mediumLongTailSafetyInv));

            //  包月安全库存-头部战略
            //  其他两个枚举不用写的原因是从db查不出数据就会设置为0
            BigDecimal headZlkhbSafetyInv =
                    headZlkhbMap.get(e.getKey()) == null ? BigDecimal.ZERO : headZlkhbMap.get(e.getKey());
            futureWeekPeak.setHeadZlkhbSafetyInv(headZlkhbSafetyInv);

            //  包月安全库存-头部非战略
            double headNotZlkhbSafetyInv = futureWeekPeak.getServiceLevelFactor().doubleValue() *
                    futureWeekPeak.getHeadNotZlkhbForecastDemandCore().doubleValue() *
                    (1 - SAFETY_INVENTORY_MAPE.get().doubleValue()) * Math.sqrt(sla / 7.0);
            futureWeekPeak.setHeadNotZlkhbSafetyInv(BigDecimal.valueOf(headNotZlkhbSafetyInv));

            //  包月安全库存 = 三者之和
            futureWeekPeak.setMonthlySafetyInv(AmountUtils.add(BigDecimal.valueOf(mediumLongTailSafetyInv),
                    headZlkhbSafetyInv, BigDecimal.valueOf(headNotZlkhbSafetyInv)));

            //  安全库存 = 包月安全库存 + 弹性备货配额 + 安全库存人工调整
            futureWeekPeak.setSafetyInv(NumberUtils.max(AmountUtils.add(futureWeekPeak.getMonthlySafetyInv(),
                    item.getBufferSafetyInv() != null ? item.getBufferSafetyInv() : BigDecimal.ZERO).add(futureWeekPeak.getSafeInvManualConfig()), BigDecimal.ZERO));

            List<DwsInventoryHealthWeeklyScaleDfDO> list = futureWeeksMap.get(e.getKey());

            futureWeekPeak.setFutureWeekMap(calFutureWeekSafetyInvMap(list, bufferMap, headZlkhbMap, statTime));

            //  周转库存
            futureWeekPeak.setTurnoverInv(forecast);
            itemBatch.add(item);
        }
        resp.putFutureWeekPeakToItem(itemBatch);
    }

    /**
     * 生成未来算法-未来13周的安全库存数据
     * 安全库存 = 包月安全库存 + 弹性备货配额
     * eg:{"w1": 当前周下一周的安全库存值}
     */
    private Map<String, InventoryForecastInfo> calFutureWeekSafetyInvMap(List<DwsInventoryHealthWeeklyScaleDfDO> source,
            Map<String, BigDecimal> bufferMap,
            Map<String, BigDecimal> headZlkhbMap, String date) {
        Map<String, List<DwsInventoryHealthWeeklyScaleDfDO>> map = ListUtils.toMapList(source, o -> "w" + (o.getWeekIndex()), o -> o);
        Map<String, InventoryForecastInfo> result = new HashMap<>();
        for (Map.Entry<String, List<DwsInventoryHealthWeeklyScaleDfDO>> entry : map.entrySet()) {
            List<DwsInventoryHealthWeeklyScaleDfDO> value = entry.getValue();
            DwsInventoryHealthWeeklyScaleDfDO anyone = value.get(0);
            String key = toKey(anyone);
            BigDecimal forecastDemandNum = NumberUtils.sum(ListUtils.filter(value,
                            o -> !Objects.equals(o.getCustomerCustomGroup(), CustomerCustomGroupEnum.HEAD_ZLKHB.getName())),
                    o -> o.getWeekPeakLogicNum());
            if (forecastDemandNum == null || forecastDemandNum.compareTo(BigDecimal.ZERO) < 0) {
                forecastDemandNum = BigDecimal.ZERO;
            }
            //  服务系数
            double serviceNum = OperationViewTools.normsinv(this.getTargetServiceLevel(date, anyone.getZoneName(), anyone.getInstanceType(), "包年包月").doubleValue());
            //  SLA
            int sla = BigDecimal.valueOf(operationViewService.getSLAByInstanceType(anyone.getInstanceType(),
                    Objects.equals(anyone.getCustomhouseTitle(), "境内"))).intValue();

            //  包月安全库存
            double monthlySafetyInventory = serviceNum * forecastDemandNum.doubleValue() *
                    (1 - SAFETY_INVENTORY_MAPE.get().doubleValue()) * Math.sqrt(sla / 7.0);
            BigDecimal zlkhbSafetyInventory = headZlkhbMap.get(key);
            if (zlkhbSafetyInventory == null) {
                zlkhbSafetyInventory = BigDecimal.ZERO;
            }

            //  弹性备货配额
            BigDecimal bufferSafetyInventory = bufferMap.get(key);

            BigDecimal safetyInv = AmountUtils.add(BigDecimal.valueOf(monthlySafetyInventory),
                    bufferSafetyInventory, zlkhbSafetyInventory);

            result.put(entry.getKey(), new InventoryForecastInfo(safetyInv, forecastDemandNum));
        }
        return result;
    }

    private String toKey(DwsInventoryHealthWeeklyScaleDfDO d) {
        return StringTools.join("@", d.getProductType(),
                d.getCustomhouseTitle(), d.getAreaName(), d.getRegionName(), d.getZoneName(),
                d.getInstanceType());
    }

    private void fillWeekDemandMap(Map<String, BigDecimal> weekDemandMap) {
        for (int i = 1; i <= 13; i++) {
            if (!weekDemandMap.containsKey("w-" + i)) {
                weekDemandMap.put("w-" + i, BigDecimal.ZERO);
            }
        }
    }

    @Override
    @HiSpeedCache(expireSecond = 600, continueFetchSecond = 3600, keyScript = "args[0]",
            useRedis = true, cacheRedisDataMillisecond = 60000)
    public List<BufferAverageCoreDTO> queryBufferScaleCoreAverage(Date date){
        if (date == null) {
            return Lang.list();
        }

        Date beginDate = DateUtils.addTime(date, Calendar.DATE, -30);
        Date endDate = date;

        Map<String, Object> params = MapUtils.of(
                "start", DateUtils.toLocalDate(beginDate),
                "end", DateUtils.toLocalDate(endDate));

        String sql = ORMUtils.getSql("/sql/operation_view/inventory_health/safety_inventory/buffer_scale_data.sql");
        List<BufferScaleDTO> raw = ckcldStdCrpDBHelper.getRaw(BufferScaleDTO.class, sql, params);

        Map<String, List<BufferScaleDTO>> map =
                ListUtils.groupBy(raw, o -> String.join("@", o.getInstanceType(), o.getZoneName()));
        return ListUtils.transform(map.entrySet(), o -> {
            BufferScaleDTO anyone = o.getValue().get(0);
            BufferAverageCoreDTO dto = new BufferAverageCoreDTO();
            dto.setCustomhouseTitle(anyone.getCustomhouseTitle());
            dto.setAreaName(anyone.getAreaName());
            dto.setRegionName(anyone.getRegionName());
            dto.setZoneName(anyone.getZoneName());
            dto.setInstanceType(anyone.getInstanceType());
            dto.setAverageStartDate(beginDate);
            dto.setAverageEndDate(endDate);
            dto.setBufferAverageCore(NumberUtils.avg(o.getValue(), 0, v -> v.getBufferCores()).intValue());
            return dto;
        });
    }

    @Override
    public OperationViewInstanceModelResp queryAllProductSummaryInstanceModel(OperationViewInstanceModelReq req) {
        /**
         * 1. 获取实例类型纬度的安全库存数据
         * 2. 根据实例类型纬度的安全库存数据，查询实例规格纬度的规模数据，并计算规模比例
         * 3. 查询人工配置的比例数据
         * 4. 拼接结果，并按照查询条件过滤
         */
        // 1. 获取安全库存数据
        OperationViewReq2 operationViewReq2 = OperationViewReq2.fromInstanceModelReq(req);
        OperationViewResp2 safeInvs = queryAllProductSummary(operationViewReq2);

        if (ListUtils.isEmpty(safeInvs.getData())) {
            return new OperationViewInstanceModelResp();
        }

        Set<String> instanceTypes = safeInvs.getData().stream().map(item -> item.getInstanceType()).collect(Collectors.toSet());
        Set<String> zoneNames = safeInvs.getData().stream().map(item -> item.getZoneName()).collect(Collectors.toSet());
        Map<String, OperationViewResp2.Item> safeInvMap = ListUtils.toMap(safeInvs.getData(), o -> Strings.join("@", o.getZoneName(), o.getInstanceType()), o -> o);

        // 2. 查询实例规格纬度的规模数据
        String sql = ORMUtils.getSql("/sql/operation_view/inventory_health/safety_inventory/instance_model_scale.sql");
        String beginScaleDate = DateUtils.formatDate(req.getScaleStartDate());
        String endScaleDate = DateUtils.formatDate(req.getScaleEndDate());
        List<String> customerTabType = CustomerCustomGroupEnum.getCustomerTabType(CustomerCustomGroupEnum.getByCode(req.getCustomerCustomGroup()));
        List<InstanceModelScaleDO> instanceModelScaleDOS = ckcldStdCrpDBHelper.getRaw(InstanceModelScaleDO.class, sql, beginScaleDate, zoneNames, instanceTypes, customerTabType, endScaleDate, zoneNames, instanceTypes, customerTabType);

        if (req.isOnlyPositiveServeScale()) {
            instanceModelScaleDOS = instanceModelScaleDOS.stream().filter(o -> o.getChangeServe().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
        }

        if (req.isOnlyPositiveBillScale()) {
            instanceModelScaleDOS = instanceModelScaleDOS.stream().filter(o -> o.getChangeBill().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
        }

        Map<String, List<InstanceModelScaleDO>> totalServeChangeNumMapList = ListUtils.toMapList(instanceModelScaleDOS, o -> Strings.join("@", o.getZoneName(), o.getInstanceType()), o -> o);
        Map<String, BigDecimal> totalServeChangeNumMap = totalServeChangeNumMapList.entrySet().stream().map(item -> {
            String key = item.getKey();
            BigDecimal value = NumberUtils.sum(item.getValue(), o -> o.getChangeServe());
            return MapUtils.of("key", key, "value", value);
        }).collect(Collectors.toMap(o -> (String) o.get("key"), o -> (BigDecimal) o.get("value")));
        Map<String, BigDecimal> totalBillChangeNumMap = totalServeChangeNumMapList.entrySet().stream().map(item -> {
            String key = item.getKey();
            BigDecimal value = NumberUtils.sum(item.getValue(), o -> o.getChangeBill());
            return MapUtils.of("key", key, "value", value);
        }).collect(Collectors.toMap(o -> (String) o.get("key"), o -> (BigDecimal) o.get("value")));

        // 3. 查询人工配置的比例数据
        InstanceModelManualConfigService instanceModelManualConfigService = SpringUtil.getBean(InstanceModelManualConfigServiceImpl.class);
        Map<String, BigDecimal> manualMap = instanceModelManualConfigService.querySnapshotManual(DateUtils.formatDate(req.getDate()));

        // 4. 拼接并过滤结果
        OperationViewInstanceModelResp resp = new OperationViewInstanceModelResp();
        List<OperationViewInstanceModelResp.Item> data = new ArrayList<>();
        // 根据用户设置的安全库存算法
        String operationViewAlgorithm = redisHelper.getString("operationViewAlgorithm");

        for (InstanceModelScaleDO scaleDO : instanceModelScaleDOS) {
            OperationViewInstanceModelResp.Item item = new OperationViewInstanceModelResp.Item();
            item.setProductType("CVM");
            item.setInstanceType(scaleDO.getInstanceType());
            item.setInstanceModel(scaleDO.getInstanceModel());
            item.setZoneName(scaleDO.getZoneName());

            item.setStartBillScale(scaleDO.getStartBill());
            item.setEndBillScale(scaleDO.getEndBill());
            item.setStartServeScale(scaleDO.getStartServe());
            item.setEndServeScale(scaleDO.getEndServe());
            item.setBillScaleChange(scaleDO.getChangeBill());
            item.setServeScaleChange(scaleDO.getChangeServe());

            OperationViewResp2.Item safeInvItem = safeInvMap.get(Strings.join("@", item.getZoneName(), item.getInstanceType()));
            BigDecimal safeInvValue = null;

            if (safeInvItem != null) {
                item.setCustomhouseTitle(safeInvItem.getCustomhouseTitle());
                item.setAreaName(safeInvItem.getAreaName());
                item.setRegionName(safeInvItem.getRegionName());
                OperationViewResp2.SafetyInventoryResult safetyInventoryResult = null;
                switch (operationViewAlgorithm) {
                    case "historyWeekPeak":
                        safetyInventoryResult = safeInvItem.getHistoryWeekPeak();
                        break;
                    case "historyWeekDiff":
                        safetyInventoryResult = safeInvItem.getHistoryWeekDiff();
                        break;
                    case "futureWeekPeak":
                        safetyInventoryResult = safeInvItem.getFutureWeekPeak();
                        break;
                    case "historyWeekPeakForecastWN":
                        safetyInventoryResult = safeInvItem.getHistoryWeekPeakForecastWN();
                        break;
                    default:
                        throw BizException.makeThrow("该安全库存算法不存在：", operationViewAlgorithm);
                }

                if (safetyInventoryResult == null) {
                    log.info(item.getZoneName() + "+" + item.getInstanceType() + " 下的安全库存不存在");
                    continue;
                } else {
                    safeInvValue = safetyInventoryResult.getSafetyInv();
                }
            } else {
                log.info(item.getZoneName() + "+" + item.getInstanceType() + " 下的安全库存不存在");
                continue;
            }

            BigDecimal totalServeChangeNum = totalServeChangeNumMap.getOrDefault(Strings.join("@", item.getZoneName(), item.getInstanceType()), BigDecimal.ZERO);
            BigDecimal totalBillChangeNum = totalBillChangeNumMap.getOrDefault(Strings.join("@", item.getZoneName(), item.getInstanceType()), BigDecimal.ZERO);

            if (totalServeChangeNum.compareTo(BigDecimal.ZERO) != 0) {
                BigDecimal serveChangeRatio = item.getServeScaleChange().divide(totalServeChangeNum, 4, RoundingMode.HALF_UP);
                item.setServeScaleChangeRatio(serveChangeRatio);
            }

            if (totalBillChangeNum.compareTo(BigDecimal.ZERO) != 0) {
                BigDecimal billChangeRatio = item.getBillScaleChange().divide(totalBillChangeNum, 4, RoundingMode.HALF_UP);
                item.setBillScaleChangeRatio(billChangeRatio);
            }

            BigDecimal manualConfig = manualMap.get(Strings.join("@", item.getZoneName(), item.getInstanceModel()));

            if (manualConfig != null) {
                item.setManualConfig(manualConfig);

                if (safeInvValue != null) {
                    item.setSafeInv(safeInvValue.multiply(manualConfig));
                }
            }

            if (req.getMinManualConfigValue() != null) {
                if (manualConfig == null || manualConfig.compareTo(req.getMinManualConfigValue()) < 0) {
                    continue;
                }
            }

            if (req.getMaxManualConfigValue() != null) {
                if (manualConfig == null || manualConfig.compareTo(req.getMaxManualConfigValue()) > 0) {
                    continue;
                }
            }

            data.add(item);
        }

        // sort data
        data.sort((a, b) -> {
            if (a.getZoneName().compareTo(b.getZoneName()) > 0) {;
                return 1;
            } else if (a.getZoneName().compareTo(b.getZoneName()) < 0) {
                return -1;
            } else {
                if (a.getInstanceType().compareTo(b.getInstanceType()) > 0) {;
                    return 1;
                } else if (a.getInstanceType().compareTo(b.getInstanceType()) < 0) {
                    return -1;
                } else {
                    if (a.getManualConfig() != null && b.getManualConfig() != null) {
                        int compare = b.getManualConfig().compareTo(a.getManualConfig());
                        return compare;
                    } else if (a.getManualConfig() != null && b.getManualConfig() == null) {
                        return -1;
                    }  else if (a.getManualConfig() == null && b.getManualConfig() != null) {
                        return 1;
                    }else {
                        return b.getServeScaleChangeRatio().compareTo(a.getServeScaleChangeRatio());
                    }
                }
            }
        });

        resp.setData(data);

        return resp;
    }


    @Data
    @Table("dws_cloud_server_level")
    static public class SlaDto {

        @Column(value = "zone_name")
        private String zone;
        @Column(value = "instance_family")
        private String ins;

        @Column(value = "sumApi", computed = "sum(api_total)")
        private BigDecimal apiTotal;

        @Column(value = "sumApiSucc", computed = "sum(api_succ_total)")
        private BigDecimal apiSuccTotal;

        @Column(value = "sumSold", computed = "sum(sold_total)")
        private BigDecimal sumSold;

        @Column(value = "sumSoldOut", computed = "sum(sold_out_total)")
        private BigDecimal sumSoldOut;
    }

    public OperationViewReq2 createNewReq(OperationViewReq2 req, String monday) {
        OperationViewReq2 mondayReq = new OperationViewReq2();
        mondayReq.setDate(DateUtils.parse(monday));
        mondayReq.setCustomerCustomGroup(req.getCustomerCustomGroup());
        mondayReq.setLineType(req.getLineType());
        mondayReq.setMaterialType(req.getMaterialType());
        mondayReq.setInvDetailType(req.getInvDetailType());
        mondayReq.setExcludeUinList(req.getExcludeUinList());
        mondayReq.setInstanceType(req.getInstanceType());
        mondayReq.setCustomhouseTitle(req.getCustomhouseTitle());
        mondayReq.setAreaName(req.getAreaName());
        mondayReq.setRegionName(req.getRegionName());
        mondayReq.setZoneName(req.getZoneName());
        mondayReq.setIsForecast(req.getIsForecast());
        mondayReq.setZoneCategory(req.getZoneCategory());
        mondayReq.setInstanceTypeCategory(req.getInstanceTypeCategory());
        mondayReq.setIsCombine(req.getIsCombine());
        mondayReq.setCategoryDate(req.getCategoryDate());
        return mondayReq;
    }

    public WhereSQL genZoneAndDeviceCategoryCondition(List<String> zoneCategory, List<String> instanceTypeCategory, Boolean isCombine, String date,List<String> customhouseTitle) {
        WhereSQL condition = new WhereSQL();
        if (ListUtils.isEmpty(zoneCategory) && ListUtils.isEmpty(instanceTypeCategory)) {
            return condition;
        }

        List<String> allZoneNames = getZoneNamesByZoneCategory(zoneCategory, date);
        WhereSQL zoneCondition = new WhereSQL();
        WhereSQL instanceTypeCondition = new WhereSQL();

        if (ListUtils.isNotEmpty(zoneCategory)) {
            zoneCondition.or("txy_zone_name in (?)", allZoneNames);
        }

        boolean containsUndefinedCategory = false;

        if (ListUtils.isNotEmpty(instanceTypeCategory)) {
            containsUndefinedCategory = instanceTypeCategory.stream().anyMatch(t -> t.equals("undefined"));
        }

        Set<String> negativeAllInstanceTypes = null;

        if (containsUndefinedCategory) {
            instanceTypeCategory = instanceTypeCategory.stream().filter(t -> !t.equals("undefined")).collect(Collectors.toList());
            negativeAllInstanceTypes = getInstanceTypesByInstanceCategory(ListUtils.newList(), date, customhouseTitle);
        }

        Set<String> allInstanceTypes = getInstanceTypesByInstanceCategory(instanceTypeCategory, date, customhouseTitle);

        //  组合机型逻辑
        List<Set<String>> combinations = dictService.queryAllCombineInstanceType().getCombination();

        if (isCombine != null && isCombine) {
            //  遍历每组组合机型数据
            for (Set<String> combination : combinations) {
                for (String each : combination) {
                    if (allInstanceTypes.contains(each)) {
                        allInstanceTypes.addAll(combination);
                        break;
                    }
                }
            }
        }
        DictServiceImpl bean = SpringUtil.getBean(DictServiceImpl.class);
        Map<String, List<String>> insToDevice = bean.getCsigInstanceTypeToDeviceTypeMap();
        if (ListUtils.isNotEmpty(instanceTypeCategory)) {
            List<String> devices = new ArrayList<>();
            for (String ins : allInstanceTypes) {
                List<String> types = insToDevice.get(ins);
                if (ListUtils.isNotEmpty(types)) {
                    devices.addAll(types);
                }
            }
            instanceTypeCondition.or("device_type in (?)", devices);
        }

        if (negativeAllInstanceTypes != null) {
            List<String> devices = new ArrayList<>();
            for (String ins : negativeAllInstanceTypes) {
                List<String> types = insToDevice.get(ins);
                if (ListUtils.isNotEmpty(types)) {
                    devices.addAll(types);
                }
            }
            instanceTypeCondition.or("device_type not in (?)", devices);
        }

        condition.and(zoneCondition);
        condition.and(instanceTypeCondition);
        return condition;
    }
}


