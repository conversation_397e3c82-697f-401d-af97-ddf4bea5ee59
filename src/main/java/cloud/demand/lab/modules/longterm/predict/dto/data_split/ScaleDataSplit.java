package cloud.demand.lab.modules.longterm.predict.dto.data_split;

import cloud.demand.lab.modules.longterm.predict.entity.LongtermPredictInputArgsDO;
import cloud.demand.lab.modules.longterm.predict.entity.LongtermPredictOutputScaleSplitDO;
import cloud.demand.lab.modules.longterm.predict.service.impl.SplitServiceImpl.ScaleSplitDependDTO;
import cloud.demand.lab.modules.longterm.predict.service.impl.SplitServiceImpl.ScaleSplitInfo;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ScaleDataSplit extends DataSplitTree<LongtermPredictOutputScaleSplitDO>{
    public ScaleDataSplit(String msg) {
        super(msg);
    }

    // 拆分顺序
    DataSplitOrder splitOrder = new ScaleDataSplitOrder();

    LongtermPredictInputArgsDO curInputArgs;

    ScaleSplitDependDTO scaleSplitDependDTO;

    public List<Map<String, Map<String, ScaleSplitInfo>>> historyList = new ArrayList<>();
}
