package cloud.demand.lab.modules.longterm.cos.service.impl;

import cloud.demand.lab.common.exception.WrongWebParameterException;
import cloud.demand.lab.common.utils.SpringUtil;
import cloud.demand.lab.modules.longterm.cos.algorithm.ARIMA;
import cloud.demand.lab.modules.longterm.cos.algorithm.DateNumDTO;
import cloud.demand.lab.modules.longterm.cos.algorithm.PROPHET;
import cloud.demand.lab.modules.longterm.cos.algorithm.PredictResult;
import cloud.demand.lab.modules.longterm.cos.entity.CosLongtermPredictCategoryConfigDO;
import cloud.demand.lab.modules.longterm.cos.entity.CosLongtermPredictInputScaleDO;
import cloud.demand.lab.modules.longterm.cos.entity.CosLongtermPredictOutScaleByAlgorithmDO;
import cloud.demand.lab.modules.longterm.cos.service.CosAlgorithmPredictService;
import cloud.demand.lab.modules.longterm.cos.service.CosBigCustomerHistoryChangeService;
import cloud.demand.lab.modules.longterm.cos.service.CosCreatePredictTaskService;
import cloud.demand.lab.modules.longterm.cos.utils.DateRangeUtils;
import cloud.demand.lab.modules.longterm.cos.web.dto.BigCustomerHistoryChangeDTO;
import cloud.demand.lab.modules.longterm.cos.web.dto.InputArgDateRangeDTO;
import cloud.demand.lab.modules.longterm.cos.web.dto.PlanCosScaleDataDTO;
import cloud.demand.lab.modules.longterm.cos.web.req.QueryAlgorithmPredictResultReq;
import cloud.demand.lab.modules.longterm.cos.web.req.QueryBigCustomerHistoryChangeReq;
import cloud.demand.lab.modules.longterm.cos.web.req.ReCalculateAlgorithmPredictReq;
import cloud.demand.lab.modules.longterm.cos.web.resp.QueryAlgorithmPredictResultResp;
import cloud.demand.lab.modules.longterm.cos.web.resp.ReCalculateAlgorithmPredictResp;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.io.IOUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.string.StringTools;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.math3.stat.regression.SimpleRegression;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class CosAlgorithmPredictServiceImpl implements CosAlgorithmPredictService {

    @Resource
    private DBHelper planCosDBHelper;
    @Resource
    private DBHelper cdLabDbHelper;
    @Resource
    private CosBigCustomerHistoryChangeService cosBigCustomerHistoryChangeService;
    @Resource
    private CosCreatePredictTaskService cosCreatePredictTaskService;

    @Override
    public QueryAlgorithmPredictResultResp queryPredictResult(QueryAlgorithmPredictResultReq req) {
        CosLongtermPredictCategoryConfigDO categoryConfig = cosCreatePredictTaskService.getCategoryById(req.getCategoryId());
        if (categoryConfig == null) {
            throw new WrongWebParameterException("方案不存在，可能已经被删除了，请刷新页面重试");
        }
        LocalDate startDate = DateRangeUtils.getPredictStartDate(categoryConfig);
        LocalDate endDate = DateRangeUtils.getPredictEndDate(categoryConfig);
        List<InputArgDateRangeDTO> dateRanges = DateRangeUtils.getDateRange(startDate, endDate, categoryConfig.getIntervalMonth());

        QueryAlgorithmPredictResultResp resp = new QueryAlgorithmPredictResultResp();
        resp.setLines(new ArrayList<>());

        // 1.1 直接查有保存下来的底表数据，如果没有就重新出发计算
        boolean isHaveData = loadHistoryDataFromDatabase(resp, req.getCategoryId(), req.getTaskId());
        if (!isHaveData) {
            log.info("任务{}没有历史数据，需要重新计算", req.getTaskId());
            ReCalculateAlgorithmPredictReq req1 = new ReCalculateAlgorithmPredictReq();
            req1.setCategoryId(req.getCategoryId());
            req1.setTaskId(req.getTaskId());
            // 走事务
            ReCalculateAlgorithmPredictResp calResp = SpringUtil.getBean(CosAlgorithmPredictService.class).reCalculateAlgorithmPredict(req1);
            if (!calResp.getIsSuccess()) {
                throw new RuntimeException("重新计算算法预测失败，请联系开发处理，错误信息：" + calResp.getErrorMsg());
            }
            loadHistoryDataFromDatabase(resp, req.getCategoryId(), req.getTaskId()); // 计算完再加载一遍
        }

        // 2.2 计算历史数据最近半年的增速
        calculateHistoryIncreaseRates(resp.getLines());

        // 直接查有保存下来的预测数据
        isHaveData = loadPredictDataFromDatabase(resp, req.getTaskId(), req.getCategoryId());
        if (!isHaveData) {
            log.info("任务{}没有预测数据，需要重新计算", req.getTaskId());
            ReCalculateAlgorithmPredictReq req1 = new ReCalculateAlgorithmPredictReq();
            req1.setCategoryId(req.getCategoryId());
            req1.setTaskId(req.getTaskId());
            // 走事务
            ReCalculateAlgorithmPredictResp calResp = SpringUtil.getBean(CosAlgorithmPredictService.class).reCalculateAlgorithmPredict(req1);
            if (!calResp.getIsSuccess()) {
                throw new RuntimeException("重新计算算法预测失败，请联系开发处理，错误信息：" + calResp.getErrorMsg());
            }
            loadPredictDataFromDatabase(resp, req.getTaskId(), req.getCategoryId()); // 计算完再加载一遍
        }

        // 2.3 计算预测数据预测时间段的增速
        calculatePredictIncreaseRates(resp.getLines(), dateRanges);

        return resp;
    }

    @Override
    @Transactional(value = "cdlabTransactionManager")
    public ReCalculateAlgorithmPredictResp reCalculateAlgorithmPredict(ReCalculateAlgorithmPredictReq req) {
        try {
            CosLongtermPredictCategoryConfigDO categoryConfig = cosCreatePredictTaskService.getCategoryById(req.getCategoryId());
            if (categoryConfig == null) {
                throw new WrongWebParameterException("方案不存在，可能已经被删除了，请刷新页面重试");
            }

            // 1. 查询大客户历史执行数据，用于在全量数据中进行剔除
            List<BigCustomerHistoryChangeDTO> bigCustomerHistoryChange =
                    cosBigCustomerHistoryChangeService.queryBigCustomerHistoryChange(
                    new QueryBigCustomerHistoryChangeReq(req.getCategoryId(), req.getTaskId())).getDataList();

            // 2. 查询历史数据，底数上分内外部，在内存中合并成全部
            QueryAlgorithmPredictResultResp tempResp = new QueryAlgorithmPredictResultResp();
            tempResp.setLines(new ArrayList<>());
            queryHistoryRealtime(tempResp, bigCustomerHistoryChange, categoryConfig);

            // 3. 保存处理完的存量数据到CosLongtermPredictInputScaleDO
            saveInputScaleData(tempResp.getLines(), req.getCategoryId(), req.getTaskId());

            // 4. 执行算法预测
            // 4.1 一次线性拟合
            linearFit(tempResp.getLines(), categoryConfig);
            // 4.2 ARIMA算法
            arimaPredict(tempResp.getLines(), categoryConfig);
            // 4.3 Prophet算法
            prophetPredict(tempResp.getLines(), categoryConfig);

            // 5. 保存算法预测结果到CosLongtermPredictOutScaleByAlgorithmDO
            saveAlgorithmPredictResults(tempResp.getLines(), req.getCategoryId(), req.getTaskId());

            ReCalculateAlgorithmPredictResp resp = new ReCalculateAlgorithmPredictResp();
            resp.setIsSuccess(true);
            return resp;
        } catch (Exception e) {
            log.error("重新计算算法预测失败，categoryId: {}, taskId: {}", req.getCategoryId(), req.getTaskId(), e);
            ReCalculateAlgorithmPredictResp resp = new ReCalculateAlgorithmPredictResp();
            resp.setIsSuccess(false);
            resp.setErrorMsg(e.getMessage());
            return resp;
        }
    }

    /**
     * 计算历史数据最近半年的增速
     * @param lines 数据线列表
     */
    private void calculateHistoryIncreaseRates(List<QueryAlgorithmPredictResultResp.Line> lines) {
        if (ListUtils.isEmpty(lines)) {
            return;
        }

        // 筛选出所有type=HISTORY的线
        List<QueryAlgorithmPredictResultResp.Line> historyLines = ListUtils.filter(lines, line -> "HISTORY".equals(line.getType()));
        if (ListUtils.isEmpty(historyLines)) {
            return;
        }

        for (QueryAlgorithmPredictResultResp.Line historyLine : historyLines) {
            try {
                calculateHistoryIncreaseRateForLine(historyLine);
            } catch (Exception e) {
                log.error("计算历史线{}的增速时发生错误", historyLine.getScope(), e);
            }
        }
    }

    /**
     * 计算单个历史线的最近半年增速
     */
    private void calculateHistoryIncreaseRateForLine(QueryAlgorithmPredictResultResp.Line historyLine) {
        if (historyLine.getPoints() == null || historyLine.getPoints().isEmpty()) {
            return;
        }

        // 按日期排序数据点
        List<List<Object>> sortedPoints = new ArrayList<>(historyLine.getPoints());
        ListUtils.sortAscNullLast(sortedPoints, point -> (LocalDate) point.get(0));
        if (sortedPoints.size() < 2) {
            return;
        }

        LocalDate lastDate = (LocalDate) sortedPoints.get(sortedPoints.size() - 1).get(0);
        LocalDate halfYearAgoDate = lastDate.minusMonths(6);

        List<Object> startPoint = null;
        List<Object> endPoint = sortedPoints.get(sortedPoints.size() - 1);

        for (List<Object> point : sortedPoints) {
            LocalDate pointDate = (LocalDate) point.get(0);
            if (!pointDate.isBefore(halfYearAgoDate)) {
                startPoint = point;
                break;
            }
        }
        // 如果找不到合适的起始点，则不计算
        if (startPoint == null) {
            return;
        }

        // 计算增速
        LocalDate startDate = (LocalDate) startPoint.get(0);
        LocalDate endDate = (LocalDate) endPoint.get(0);
        BigDecimal startValue = (BigDecimal) startPoint.get(1);
        BigDecimal endValue = (BigDecimal) endPoint.get(1);

        if (startValue.compareTo(BigDecimal.ZERO) == 0) {
            return; // 避免除零
        }

        BigDecimal rate = endValue.subtract(startValue).divide(startValue, 4, RoundingMode.HALF_UP);

        // 创建增速信息
        QueryAlgorithmPredictResultResp.IncreaseInfo increaseInfo = new QueryAlgorithmPredictResultResp.IncreaseInfo();
        increaseInfo.setDateName("近半年增速");
        increaseInfo.setStartDate(startDate);
        increaseInfo.setEndDate(endDate);
        increaseInfo.setRate(rate);

        // 添加到历史线的增速信息中
        if (historyLine.getIncreaseInfos() == null) {
            historyLine.setIncreaseInfos(new ArrayList<>());
        }
        historyLine.getIncreaseInfos().add(increaseInfo);
    }

    /**
     * 计算预测数据各时间段的增速
     * @param lines 数据线列表
     * @param dateRanges 时间段范围列表
     */
    private void calculatePredictIncreaseRates(List<QueryAlgorithmPredictResultResp.Line> lines, List<InputArgDateRangeDTO> dateRanges) {
        if (ListUtils.isEmpty(lines) || ListUtils.isEmpty(dateRanges)) {
            return;
        }

        // 筛选出所有type=PREDICT的线
        List<QueryAlgorithmPredictResultResp.Line> predictLines = ListUtils.filter(lines, line -> "PREDICT".equals(line.getType()));
        if (ListUtils.isEmpty(predictLines)) {
            return;
        }

        // 创建历史数据的映射，用于补充预测数据中缺失的时间点
        Map<String, QueryAlgorithmPredictResultResp.Line> historyLineMap = new HashMap<>();
        List<QueryAlgorithmPredictResultResp.Line> historyLines = ListUtils.filter(lines, line -> "HISTORY".equals(line.getType()));
        for (QueryAlgorithmPredictResultResp.Line historyLine : historyLines) {
            historyLineMap.put(historyLine.getScope(), historyLine);
        }

        for (QueryAlgorithmPredictResultResp.Line predictLine : predictLines) {
            try {
                calculatePredictIncreaseRateForLine(predictLine, dateRanges, historyLineMap.get(predictLine.getScope()));
            } catch (Exception e) {
                log.error("计算预测线{}的增速时发生错误", predictLine.getScope(), e);
            }
        }
    }

    /**
     * 计算单个预测线各时间段的增速
     */
    private void calculatePredictIncreaseRateForLine(QueryAlgorithmPredictResultResp.Line predictLine,
                                                     List<InputArgDateRangeDTO> dateRanges,
                                                     QueryAlgorithmPredictResultResp.Line historyLine) {
        if (predictLine.getPoints() == null) {
            return;
        }
        if (predictLine.getIncreaseInfos() == null) {
            predictLine.setIncreaseInfos(new ArrayList<>());
        }

        // 合并历史数据和预测数据的点，用于查找时间点
        Map<LocalDate, BigDecimal> allDataPoints = new HashMap<>();

        // 添加预测数据点
        for (List<Object> point : predictLine.getPoints()) {
            LocalDate date = (LocalDate) point.get(0);
            BigDecimal value = (BigDecimal) point.get(1);
            allDataPoints.put(date, value);
        }

        // 添加历史数据点（历史数据优先级更高）
        if (historyLine != null && historyLine.getPoints() != null) {
            for (List<Object> point : historyLine.getPoints()) {
                LocalDate date = (LocalDate) point.get(0);
                BigDecimal value = (BigDecimal) point.get(1);
                allDataPoints.put(date, value);
            }
        }

        // 计算各时间段的增速
        for (int i = 0; i < dateRanges.size(); i++) {
            InputArgDateRangeDTO currentRange = dateRanges.get(i);
            // 获取当前时间段结束时的值
            BigDecimal endValue = allDataPoints.get(currentRange.getEndDate());
            if (endValue == null) {
                continue;
            }
            BigDecimal startValue = allDataPoints.get(currentRange.getStartDate());
            if (startValue == null || startValue.compareTo(BigDecimal.ZERO) == 0) {
                continue; // 避免除零或找不到起始值
            }

            BigDecimal rate = endValue.subtract(startValue).divide(startValue, 4, RoundingMode.HALF_UP);

            // 创建增速信息
            QueryAlgorithmPredictResultResp.IncreaseInfo increaseInfo = new QueryAlgorithmPredictResultResp.IncreaseInfo();
            increaseInfo.setDateName(currentRange.getDateName());
            increaseInfo.setStartDate(currentRange.getStartDate());
            increaseInfo.setEndDate(currentRange.getEndDate());
            increaseInfo.setRate(rate);

            predictLine.getIncreaseInfos().add(increaseInfo);
        }
    }

    private void arimaPredict(List<QueryAlgorithmPredictResultResp.Line> lines, CosLongtermPredictCategoryConfigDO categoryConfig) {
        if (lines == null || lines.isEmpty() || categoryConfig == null) {
            return;
        }

        // 筛选出所有type=HISTORY的线
        List<QueryAlgorithmPredictResultResp.Line> historyLines = ListUtils.filter(lines, line -> "HISTORY".equals(line.getType()));
        if (historyLines.isEmpty()) {
            return;
        }

        String predictEndStr = categoryConfig.getPredictEnd();
        if (StringTools.isBlank(predictEndStr)) {
            return;
        }

        // 解析预测结束时间，获取该月份的最后一天
        LocalDate predictEndDate;
        try {
            YearMonth predictEndYearMonth = YearMonth.parse(predictEndStr);
            predictEndDate = predictEndYearMonth.atEndOfMonth();
        } catch (Exception e) {
            log.error("解析预测结束时间失败: {}", predictEndStr, e);
            return;
        }

        // 对每个历史线进行ARIMA预测
        for (QueryAlgorithmPredictResultResp.Line historyLine : historyLines) {
            try {
                performArimaPredictForLine(historyLine, predictEndDate, lines, categoryConfig.getArimaArgs());
            } catch (Exception e) {
                log.error("对线{}进行ARIMA预测时发生错误", historyLine.getScope(), e);
            }
        }
    }

    /**
     * 对单个历史线进行ARIMA预测
     */
    private void performArimaPredictForLine(QueryAlgorithmPredictResultResp.Line historyLine, LocalDate predictEndDate,
                                            List<QueryAlgorithmPredictResultResp.Line> lines, String arimaArgs) {
        if (historyLine.getPoints() == null || historyLine.getPoints().isEmpty()) {
            return;
        }

        // 按日期排序历史数据点
        List<List<Object>> sortedPoints = new ArrayList<>(historyLine.getPoints());
        ListUtils.sortAscNullLast(sortedPoints, point -> (LocalDate) point.get(0));

        // 找到历史数据的最后日期
        LocalDate lastHistoryDate = (LocalDate) sortedPoints.get(sortedPoints.size() - 1).get(0);

        // 预测开始日期是历史数据结束日期的下一天
        LocalDate predictStartDate = lastHistoryDate.plusDays(1);

        if (predictStartDate.isAfter(predictEndDate)) {
            log.warn("ARIMA预测开始日期{}晚于结束日期{}，跳过预测", predictStartDate, predictEndDate);
            return;
        }

        // 准备ARIMA输入数据
        List<DateNumDTO> arimaInputData = new ArrayList<>();
        for (List<Object> point : sortedPoints) {
            LocalDate date = (LocalDate) point.get(0);
            BigDecimal value = (BigDecimal) point.get(1);
            String dateStr = DateUtils.format(date, "yyyy-MM-dd");
            arimaInputData.add(new DateNumDTO(dateStr, value));
        }

        if (arimaInputData.size() < 3) {
            log.warn("ARIMA预测数据点不足，scope: {}, 数据点数量: {}", historyLine.getScope(), arimaInputData.size());
            return;
        }

        // 计算需要预测的天数
        long predictDays = ChronoUnit.DAYS.between(predictStartDate, predictEndDate) + 1;
        if (predictDays <= 0) {
            return;
        }

        try {
            // 使用ARIMA进行预测
            List<Integer> arimaParams = ListUtils.of(1,2,1);
            if (StringTools.isNotBlank(arimaArgs)) {
                arimaParams = ListUtils.transform(ListUtils.of(arimaArgs.split(",")), Integer::parseInt);
            }

            PredictResult predictResult = ARIMA.predict(arimaInputData, (int) predictDays, arimaParams);

            if (predictResult.getData() == null || predictResult.getData().isEmpty()) {
                log.warn("ARIMA预测返回空结果，scope: {}", historyLine.getScope());
                return;
            }

            // 创建预测线
            QueryAlgorithmPredictResultResp.Line predictLine = new QueryAlgorithmPredictResultResp.Line();
            predictLine.setScope(historyLine.getScope());
            predictLine.setType("PREDICT");
            predictLine.setAlgorithm("ARIMA");
            predictLine.setIncreaseInfos(new ArrayList<>());
            predictLine.setPoints(new ArrayList<>());

            // 设置算法参数
            Map<String, Object> algorithmParams = new HashMap<>();
            if (StringTools.isNotBlank(predictResult.getFallbackAlgorithm())) {
                algorithmParams.put("fallbackAlgorithm", predictResult.getFallbackAlgorithm());
            }
            predictLine.setAlgorithmParams(algorithmParams);

            // 转换预测结果为预测点
            LocalDate currentPredictDate = predictStartDate;
            for (DateNumDTO predictData : predictResult.getData()) {
                if (currentPredictDate.isAfter(predictEndDate)) {
                    break;
                }
                predictLine.getPoints().add(ListUtils.of(currentPredictDate, predictData.getValue()));
                currentPredictDate = currentPredictDate.plusDays(1);
            }

            // 将预测线添加到结果中
            lines.add(predictLine);

        } catch (Exception e) {
            log.error("ARIMA预测失败，scope: {}", historyLine.getScope(), e);
        }
    }

    /**
     * Prophet算法预测
     */
    private void prophetPredict(List<QueryAlgorithmPredictResultResp.Line> lines, CosLongtermPredictCategoryConfigDO categoryConfig) {
        if (lines == null || lines.isEmpty() || categoryConfig == null) {
            return;
        }

        // 筛选出所有type=HISTORY的线
        List<QueryAlgorithmPredictResultResp.Line> historyLines = ListUtils.filter(lines, line -> "HISTORY".equals(line.getType()));
        if (historyLines.isEmpty()) {
            return;
        }

        String predictEndStr = categoryConfig.getPredictEnd();
        if (StringTools.isBlank(predictEndStr)) {
            return;
        }

        // 解析预测结束时间，获取该月份的最后一天
        LocalDate predictEndDate;
        try {
            YearMonth predictEndYearMonth = YearMonth.parse(predictEndStr);
            predictEndDate = predictEndYearMonth.atEndOfMonth();
        } catch (Exception e) {
            log.error("解析预测结束时间失败: {}", predictEndStr, e);
            return;
        }

        // 对每个历史线进行Prophet预测
        for (QueryAlgorithmPredictResultResp.Line historyLine : historyLines) {
            try {
                performProphetPredictForLine(historyLine, predictEndDate, lines);
            } catch (Exception e) {
                log.error("对线{}进行Prophet预测时发生错误", historyLine.getScope(), e);
            }
        }
    }

    /**
     * 对单个历史线进行Prophet预测
     */
    private void performProphetPredictForLine(QueryAlgorithmPredictResultResp.Line historyLine, LocalDate predictEndDate,
                                              List<QueryAlgorithmPredictResultResp.Line> lines) {
        if (historyLine.getPoints() == null || historyLine.getPoints().isEmpty()) {
            return;
        }

        // 获取历史数据的最后一个时间点
        List<List<Object>> sortedPoints = new ArrayList<>(historyLine.getPoints());
        ListUtils.sortAscNullLast(sortedPoints, point -> (LocalDate) point.get(0));

        LocalDate lastHistoryDate = (LocalDate) sortedPoints.get(sortedPoints.size() - 1).get(0);
        LocalDate predictStartDate = lastHistoryDate.plusDays(1);

        if (predictStartDate.isAfter(predictEndDate)) {
            return;
        }

        // 准备Prophet输入数据
        List<DateNumDTO> prophetInputData = new ArrayList<>();
        for (List<Object> point : sortedPoints) {
            LocalDate date = (LocalDate) point.get(0);
            BigDecimal value = (BigDecimal) point.get(1);
            String dateStr = DateUtils.format(date, "yyyy-MM-dd");
            prophetInputData.add(new DateNumDTO(dateStr, value));
        }

        if (prophetInputData.size() < 3) {
            log.warn("Prophet预测数据点不足，scope: {}, 数据点数量: {}", historyLine.getScope(), prophetInputData.size());
            return;
        }

        // 计算需要预测的天数
        long predictDays = ChronoUnit.DAYS.between(predictStartDate, predictEndDate) + 1;
        if (predictDays <= 0) {
            return;
        }

        try {
            // 使用Prophet进行预测
            PredictResult predictResult = PROPHET.predict(prophetInputData, (int) predictDays);

            if (predictResult.getData() == null || predictResult.getData().isEmpty()) {
                log.warn("Prophet预测返回空结果，scope: {}", historyLine.getScope());
                return;
            }

            // 创建预测线
            QueryAlgorithmPredictResultResp.Line predictLine = new QueryAlgorithmPredictResultResp.Line();
            predictLine.setScope(historyLine.getScope());
            predictLine.setType("PREDICT");
            predictLine.setAlgorithm("Prophet");
            predictLine.setIncreaseInfos(new ArrayList<>());
            predictLine.setPoints(new ArrayList<>());

            // 设置算法参数信息
            Map<String, Object> algorithmParams = new HashMap<>();
            predictLine.setAlgorithmParams(algorithmParams);

            // 转换预测结果为预测点
            LocalDate currentPredictDate = predictStartDate;
            for (DateNumDTO predictData : predictResult.getData()) {
                if (currentPredictDate.isAfter(predictEndDate)) {
                    break;
                }
                predictLine.getPoints().add(ListUtils.of(currentPredictDate, predictData.getValue()));
                currentPredictDate = currentPredictDate.plusDays(1);
            }

            // 将预测线添加到结果中
            lines.add(predictLine);

        } catch (Exception e) {
            log.error("Prophet预测失败，scope: {}", historyLine.getScope(), e);
        }
    }

    private void linearFit(List<QueryAlgorithmPredictResultResp.Line> lines, CosLongtermPredictCategoryConfigDO categoryConfig) {
        if (lines == null || lines.isEmpty() || categoryConfig == null) {
            return;
        }

        // 筛选出所有type=HISTORY的线
        List<QueryAlgorithmPredictResultResp.Line> historyLines = ListUtils.filter(lines, line -> "HISTORY".equals(line.getType()));
        if (historyLines.isEmpty()) {
            return;
        }

        LocalDate linearStartDate = categoryConfig.getLinearStartDate();
        String predictEndStr = categoryConfig.getPredictEnd();
        if (linearStartDate == null || StringTools.isBlank(predictEndStr)) {
            return;
        }

        // 解析预测结束时间，获取该月份的最后一天
        LocalDate predictEndDate;
        try {
            YearMonth predictEndYearMonth = YearMonth.parse(predictEndStr);
            predictEndDate = predictEndYearMonth.atEndOfMonth();
        } catch (Exception e) {
            log.error("解析预测结束时间失败: {}", predictEndStr, e);
            return;
        }

        // 对每个历史线进行线性拟合预测
        for (QueryAlgorithmPredictResultResp.Line historyLine : historyLines) {
            try {
                performLinearFitForLine(historyLine, linearStartDate, predictEndDate, lines);
            } catch (Exception e) {
                log.error("对线{}进行线性拟合时发生错误", historyLine.getScope(), e);
            }
        }
    }

    /**
     * 对单个历史线进行线性拟合预测
     */
    private void performLinearFitForLine(QueryAlgorithmPredictResultResp.Line historyLine, LocalDate linearStartDate,
                                         LocalDate predictEndDate, List<QueryAlgorithmPredictResultResp.Line> lines) {
        if (historyLine.getPoints() == null || historyLine.getPoints().isEmpty()) {
            return;
        }

        // 筛选出从linearStartDate开始的数据点，并按日期排序
        List<List<Object>> filteredPoints = new ArrayList<>();
        LocalDate lastDataDate = null;

        for (List<Object> point : historyLine.getPoints()) {
            if (point.size() >= 2) {
                LocalDate date = (LocalDate) point.get(0);
                if (!date.isBefore(linearStartDate)) {
                    filteredPoints.add(point);
                    if (lastDataDate == null || date.isAfter(lastDataDate)) {
                        lastDataDate = date;
                    }
                }
            }
        }

        // 按日期排序
        ListUtils.sortAscNullLast(filteredPoints, point -> (LocalDate) point.get(0));

        if (filteredPoints.size() < 2) {
            log.warn("线性拟合数据点不足，scope: {}, 数据点数量: {}", historyLine.getScope(), filteredPoints.size());
            return;
        }

        // 准备线性回归数据
        SimpleRegression regression = new SimpleRegression();

        for (List<Object> point : filteredPoints) {
            LocalDate date = (LocalDate) point.get(0);
            BigDecimal value = (BigDecimal) point.get(1);

            // 将日期转换为从linearStartDate开始的天数索引
            long dayIndex = ChronoUnit.DAYS.between(linearStartDate, date);
            regression.addData(dayIndex, value.doubleValue());
        }

        // 获取线性拟合参数
        double slope = regression.getSlope();        // a (斜率)
        double intercept = regression.getIntercept(); // b (截距)
        double rSquared = regression.getRSquare();    // r²

        // 创建预测线
        QueryAlgorithmPredictResultResp.Line predictLine = new QueryAlgorithmPredictResultResp.Line();
        predictLine.setScope(historyLine.getScope());
        predictLine.setType("PREDICT");
        predictLine.setAlgorithm("线性拟合");
        predictLine.setIncreaseInfos(new ArrayList<>());
        predictLine.setPoints(new ArrayList<>());

        // 设置算法参数
        Map<String, Object> algorithmParams = new HashMap<>();
        algorithmParams.put("a", BigDecimal.valueOf(slope));
        algorithmParams.put("b", BigDecimal.valueOf(intercept));
        algorithmParams.put("r2", BigDecimal.valueOf(rSquared));
        predictLine.setAlgorithmParams(algorithmParams);

        // 生成预测数据点：从linearStartDate到predictEndDate
        generatePredictPoints(predictLine, linearStartDate, predictEndDate, slope, intercept);

        // 将预测线添加到结果中
        lines.add(predictLine);
    }

    /**
     * 生成预测数据点
     */
    private void generatePredictPoints(QueryAlgorithmPredictResultResp.Line predictLine, LocalDate linearStartDate,
                                       LocalDate predictEndDate, double slope, double intercept) {
        LocalDate currentDate = linearStartDate;

        while (!currentDate.isAfter(predictEndDate)) {
            // 计算日期索引（从linearStartDate开始的天数）
            long dayIndex = ChronoUnit.DAYS.between(linearStartDate, currentDate);

            // 使用线性方程 y = ax + b 计算预测值
            double predictValue = slope * dayIndex + intercept;

            // 添加预测点
            predictLine.getPoints().add(ListUtils.of(currentDate, BigDecimal.valueOf(predictValue)));

            // 移动到下一天
            currentDate = currentDate.plusDays(1);
        }
    }

    @SneakyThrows
    private void queryHistoryRealtime(QueryAlgorithmPredictResultResp resp, List<BigCustomerHistoryChangeDTO> bigCustomerHistoryChange,
                                      CosLongtermPredictCategoryConfigDO categoryConfig) {
        String sql = IOUtils.readClasspathResourceAsString("/sql/longterm_predict/cos/plan_cos_scale.sql");
        sql = sql.replace("${CONDITION}", categoryConfig.getWhereSql());

        List<PlanCosScaleDataDTO> historyData = planCosDBHelper.getRaw(PlanCosScaleDataDTO.class, sql);

        // 3. 按scope分组数据
        Map<String, List<PlanCosScaleDataDTO>> scopeGroupedData = ListUtils.toMapList(historyData,
                o -> o.getScope(), o -> o);

        // 4.1 构建内部数据线
        QueryAlgorithmPredictResultResp.Line innerLine = buildHistoryLine("内部", scopeGroupedData.get("内部"),
                ListUtils.filter(bigCustomerHistoryChange, o -> !o.getIsOutCustomer()));
        resp.getLines().add(innerLine);
        // 4.2 构建外部数据线
        QueryAlgorithmPredictResultResp.Line outerLine = buildHistoryLine("外部", scopeGroupedData.get("外部"),
                ListUtils.filter(bigCustomerHistoryChange, o -> o.getIsOutCustomer()));
        resp.getLines().add(outerLine);

        // 4.3 构建全部数据线（内部+外部）
        QueryAlgorithmPredictResultResp.Line totalLine = buildTotalHistoryLine(innerLine, outerLine);
        resp.getLines().add(totalLine);
    }

    /**
     * 构建历史数据线
     * @param bigCustomerHistoryChange 大客户历史变动数据，在这个处理中，会将大客户的历史变动从总量中剔除
     */
    private QueryAlgorithmPredictResultResp.Line buildHistoryLine(String scope, List<PlanCosScaleDataDTO> data,
                                                                  List<BigCustomerHistoryChangeDTO> bigCustomerHistoryChange) {
        QueryAlgorithmPredictResultResp.Line line = new QueryAlgorithmPredictResultResp.Line();
        line.setScope(scope);
        line.setType("HISTORY");
        line.setIncreaseInfos(new ArrayList<>());
        line.setPoints(new ArrayList<>());
        if (data != null) {
            for (PlanCosScaleDataDTO dto : data) {
                line.getPoints().add(ListUtils.of(dto.getDate(), dto.getValue()));
            }
        }

        // 剔除大客户的历史变化量，剔除时间从早到晚进行
        ListUtils.sortAscNullLast(bigCustomerHistoryChange, o -> o.getStartDate());
        // 对每个大客户历史变动进行调整
        if (bigCustomerHistoryChange != null && !bigCustomerHistoryChange.isEmpty()) {
            for (BigCustomerHistoryChangeDTO change : bigCustomerHistoryChange) {
                LocalDate startDate = change.getStartDate();
                LocalDate endDate = change.getEndDate();
                BigDecimal delta = change.getNetChange();

                if (startDate != null && delta != null) {
                    delta = delta.negate(); // 底表录的是大客户的历史执行量，在剔除时，等于是量反过来

                    // 先进行趋势调整（在指定时间段内）
                    adjustTrend(line.getPoints(), startDate, endDate, delta);
                    // 再进行直接调整（在结束时间之后的所有数据点）
                    if (endDate != null) {
                        adjustDirect(line.getPoints(), endDate.plusDays(1), null, delta);
                    }
                }
            }
        }

        return line;
    }

    /**
     * 构建全部数据线（内部+外部合并）
     */
    private QueryAlgorithmPredictResultResp.Line buildTotalHistoryLine(QueryAlgorithmPredictResultResp.Line innerLine, QueryAlgorithmPredictResultResp.Line outerLine) {
        // 按日期分组，然后合并内外部数据
        Map<LocalDate, BigDecimal> dateValueMap = new HashMap<>();

        // 处理内部数据线的点
        if (innerLine != null && innerLine.getPoints() != null) {
            for (List<Object> point : innerLine.getPoints()) {
                LocalDate date = (LocalDate) point.get(0);
                BigDecimal value = (BigDecimal) point.get(1);
                dateValueMap.merge(date, value, BigDecimal::add);
            }
        }

        // 处理外部数据线的点
        if (outerLine != null && outerLine.getPoints() != null) {
            for (List<Object> point : outerLine.getPoints()) {
                LocalDate date = (LocalDate) point.get(0);
                BigDecimal value = (BigDecimal) point.get(1);
                dateValueMap.merge(date, value, BigDecimal::add);
            }
        }

        QueryAlgorithmPredictResultResp.Line line = new QueryAlgorithmPredictResultResp.Line();
        line.setScope("全部");
        line.setType("HISTORY");
        line.setIncreaseInfos(new ArrayList<>());
        line.setPoints(new ArrayList<>());
        for (Map.Entry<LocalDate, BigDecimal> entry : dateValueMap.entrySet()) {
            line.getPoints().add(ListUtils.of(entry.getKey(), entry.getValue()));
        }
        ListUtils.sortAscNullLast(line.getPoints(), o -> (LocalDate) o.get(0));

        return line;
    }

    /**
     * 按趋势调整delta值，如果delta为正数，则表示调高趋势；如果delta为负数，则表示调低趋势。
     *
     * @param points 数据点列表，格式为[LocalDate, BigDecimal]
     * @param startDate 开始日期
     * @param endDate 结束日期，如果为null则使用最后一个数据点的日期
     * @param delta 调整量
     */
    private void adjustTrend(List<List<Object>> points, LocalDate startDate, LocalDate endDate, BigDecimal delta) {
        if (ListUtils.isEmpty(points) || startDate == null || delta == null) {
            return;
        }

        // 如果endDate为null，使用最后一个数据点的日期
        if (endDate == null) {
            endDate = (LocalDate) points.get(points.size() - 1).get(0);
        }

        // 找到开始和结束日期对应的数据点
        BigDecimal startValue = null;
        BigDecimal endValue = null;

        for (List<Object> point : points) {
            LocalDate date = (LocalDate) point.get(0);
            BigDecimal value = (BigDecimal) point.get(1);

            if (date.equals(startDate)) {
                startValue = value;
            }
            if (date.equals(endDate)) {
                endValue = value;
            }
        }

        // 如果找不到对应的数据点，则不进行调整
        if (startValue == null || endValue == null) {
            return;
        }

        // 对指定时间段内的数据点进行趋势调整
        for (List<Object> point : points) {
            LocalDate date = (LocalDate) point.get(0);
            BigDecimal value = (BigDecimal) point.get(1);

            if (!date.isBefore(startDate) && !date.isAfter(endDate)) {
                // 应用趋势调整公式: (y + delta) - (y - value) / (y - x) * (y - x + delta)
                // 其中 x = startValue, y = endValue, value = 当前值
                BigDecimal adjustedValue;
                if (endValue.equals(startValue)) {
                    // 避免除零，如果起始值相等，直接加上delta
                    adjustedValue = value.add(delta);
                } else {
                    // 重新理解Python公式: new_value = (y + delta) - (y - value) / (y - x) * (y - x + delta)
                    BigDecimal yPlusDelta = endValue.add(delta);
                    BigDecimal yMinusValue = endValue.subtract(value);
                    BigDecimal yMinusX = endValue.subtract(startValue);
                    BigDecimal yMinusXPlusDelta = yMinusX.add(delta);

                    if (yMinusX.compareTo(BigDecimal.ZERO) == 0) {
                        // 如果y == x，直接加delta
                        adjustedValue = value.add(delta);
                    } else {
                        BigDecimal ratio = yMinusValue.divide(yMinusX, 6, RoundingMode.HALF_UP);
                        adjustedValue = yPlusDelta.subtract(ratio.multiply(yMinusXPlusDelta));
                    }
                }

                point.set(1, adjustedValue);
            }
        }
    }

    /**
     * 直接调整指定时间段内的值，适用于直接剔除或添加固定量。
     *
     * @param points 数据点列表，格式为[LocalDate, BigDecimal]
     * @param startDate 开始日期，如果为null则从第一个数据点开始
     * @param endDate 结束日期，如果为null则到最后一个数据点结束
     * @param delta 调整量
     */
    private void adjustDirect(List<List<Object>> points, LocalDate startDate, LocalDate endDate, BigDecimal delta) {
        if (ListUtils.isEmpty(points) || delta == null) {
            return;
        }

        // 如果startDate为null，从第一个数据点开始
        if (startDate == null) {
            startDate = (LocalDate) points.get(0).get(0);
        }

        // 如果endDate为null，到最后一个数据点结束
        if (endDate == null) {
            endDate = (LocalDate) points.get(points.size() - 1).get(0);
        }

        // 对指定时间段内的数据点进行直接调整
        for (List<Object> point : points) {
            LocalDate date = (LocalDate) point.get(0);
            BigDecimal value = (BigDecimal) point.get(1);

            if (!date.isBefore(startDate) && !date.isAfter(endDate)) {
                point.set(1, value.add(delta));
            }
        }
    }

    /**
     * 保存处理完的存量数据到CosLongtermPredictInputScaleDO
     */
    private void saveInputScaleData(List<QueryAlgorithmPredictResultResp.Line> lines, Long categoryId, Long taskId) {
        if (ListUtils.isEmpty(lines) || taskId == null) {
            return;
        }

        List<CosLongtermPredictInputScaleDO> inputScaleList = new ArrayList<>();

        // 筛选出历史数据线
        List<QueryAlgorithmPredictResultResp.Line> historyLines = ListUtils.filter(lines, line -> "HISTORY".equals(line.getType()));

        for (QueryAlgorithmPredictResultResp.Line line : historyLines) {
            if (line.getPoints() == null || line.getPoints().isEmpty()) {
                continue;
            }

            // 判断是否为外部客户
            Integer isOutCustomer = "外部".equals(line.getScope()) ? 1 : ("内部".equals(line.getScope()) ? 0 : -1);

            for (List<Object> point : line.getPoints()) {
                if (point.size() >= 2) {
                    LocalDate date = (LocalDate) point.get(0);
                    BigDecimal value = (BigDecimal) point.get(1);

                    CosLongtermPredictInputScaleDO inputScale = new CosLongtermPredictInputScaleDO();
                    inputScale.setTaskId(taskId);
                    inputScale.setCategoryId(categoryId);
                    inputScale.setDate(date);
                    inputScale.setIsOutCustomer(isOutCustomer);
                    inputScale.setCurScale(value);

                    inputScaleList.add(inputScale);
                }
            }
        }

        if (!inputScaleList.isEmpty()) {
            // 先删除该任务的旧数据
            cdLabDbHelper.delete(CosLongtermPredictInputScaleDO.class, "where task_id = ? and category_id=?", taskId, categoryId);
            // 批量插入新数据
            cdLabDbHelper.insertBatchWithoutReturnId(inputScaleList);
            log.info("保存存量数据成功，taskId: {}, 数据量: {}", taskId, inputScaleList.size());
        }
    }

    /**
     * 保存算法预测结果到CosLongtermPredictOutScaleByAlgorithmDO
     */
    private void saveAlgorithmPredictResults(List<QueryAlgorithmPredictResultResp.Line> lines, Long categoryId, Long taskId) {
        if (ListUtils.isEmpty(lines) || taskId == null) {
            return;
        }

        List<CosLongtermPredictOutScaleByAlgorithmDO> predictResultList = new ArrayList<>();

        // 筛选出预测数据线
        List<QueryAlgorithmPredictResultResp.Line> predictLines = ListUtils.filter(lines, line -> "PREDICT".equals(line.getType()));

        for (QueryAlgorithmPredictResultResp.Line line : predictLines) {
            if (line.getPoints() == null || line.getPoints().isEmpty()) {
                continue;
            }

            // 判断是否为外部客户
            Integer isOutCustomer = "外部".equals(line.getScope()) ? 1 : ("内部".equals(line.getScope()) ? 0 : -1);

            for (List<Object> point : line.getPoints()) {
                if (point.size() >= 2) {
                    LocalDate date = (LocalDate) point.get(0);
                    BigDecimal value = (BigDecimal) point.get(1);

                    CosLongtermPredictOutScaleByAlgorithmDO predictResult = new CosLongtermPredictOutScaleByAlgorithmDO();
                    predictResult.setTaskId(taskId);
                    predictResult.setCategoryId(categoryId);
                    predictResult.setAlgorithm(line.getAlgorithm());
                    predictResult.setDate(date);
                    predictResult.setIsOutCustomer(isOutCustomer);
                    predictResult.setPredictScale(value);

                    predictResultList.add(predictResult);
                }
            }
        }

        if (!predictResultList.isEmpty()) {
            // 先删除该任务的旧数据
            cdLabDbHelper.delete(CosLongtermPredictOutScaleByAlgorithmDO.class, "where task_id = ? and category_id=?", taskId, categoryId);
            // 批量插入新数据
            cdLabDbHelper.insertBatchWithoutReturnId(predictResultList);
            log.info("保存算法预测结果成功，taskId: {}, 数据量: {}", taskId, predictResultList.size());
        }
    }

    /**
     * 从数据库加载历史数据
     * @return 有数据时返回true，否则返回false
     */
    private boolean loadHistoryDataFromDatabase(QueryAlgorithmPredictResultResp resp, Long categoryId, Long taskId) {
        // 查询存量数据
        List<CosLongtermPredictInputScaleDO> inputScaleList = cdLabDbHelper.getAll(
                CosLongtermPredictInputScaleDO.class, "where task_id=? and category_id=? order by date", taskId, categoryId);
        if (ListUtils.isEmpty(inputScaleList)) {
            return false;
        }

        // 按scope分组数据
        Map<String, List<CosLongtermPredictInputScaleDO>> scopeGroupedData = new HashMap<>();
        for (CosLongtermPredictInputScaleDO inputScale : inputScaleList) {
            String scope = getScopeName(inputScale.getIsOutCustomer());
            scopeGroupedData.computeIfAbsent(scope, k -> new ArrayList<>()).add(inputScale);
        }

        // 转换为响应格式
        for (Map.Entry<String, List<CosLongtermPredictInputScaleDO>> entry : scopeGroupedData.entrySet()) {
            QueryAlgorithmPredictResultResp.Line line = new QueryAlgorithmPredictResultResp.Line();
            line.setScope(entry.getKey());
            line.setType("HISTORY");
            line.setIncreaseInfos(new ArrayList<>());
            line.setPoints(new ArrayList<>());

            for (CosLongtermPredictInputScaleDO inputScale : entry.getValue()) {
                line.getPoints().add(ListUtils.of(inputScale.getDate(), inputScale.getCurScale()));
            }

            // 按日期排序
            ListUtils.sortAscNullLast(line.getPoints(), o -> (LocalDate) o.get(0));
            resp.getLines().add(line);
        }

        return true;
    }

    private String getScopeName(Integer isOutCustomer) {
        if (isOutCustomer == null) {
            return "未知";
        } else if (isOutCustomer.equals(1)) {
            return "外部";
        } else if (isOutCustomer.equals(0)) {
            return "内部";
        } else if (isOutCustomer.equals(-1)){
            return "全部";
        } else {
            return "未知";
        }
    }

    /**
     * 从数据库加载预测数据
     */
    private boolean loadPredictDataFromDatabase(QueryAlgorithmPredictResultResp resp, Long taskId, Long categoryId) {
        // 查询算法预测结果数据
        List<CosLongtermPredictOutScaleByAlgorithmDO> predictResultList = cdLabDbHelper.getAll(
                CosLongtermPredictOutScaleByAlgorithmDO.class, "where task_id = ? and category_id=? order by algorithm, date", taskId, categoryId);
        if (ListUtils.isEmpty(predictResultList)) {
            return false;
        }

        // 按算法和scope分组数据
        Map<String, Map<String, List<CosLongtermPredictOutScaleByAlgorithmDO>>> algorithmScopeGroupedData = new HashMap<>();
        for (CosLongtermPredictOutScaleByAlgorithmDO predictResult : predictResultList) {
            String algorithm = predictResult.getAlgorithm();
            String scope = getScopeName(predictResult.getIsOutCustomer());

            algorithmScopeGroupedData.computeIfAbsent(algorithm, k -> new HashMap<>())
                    .computeIfAbsent(scope, k -> new ArrayList<>()).add(predictResult);
        }

        // 转换为响应格式
        for (Map.Entry<String, Map<String, List<CosLongtermPredictOutScaleByAlgorithmDO>>> algorithmEntry : algorithmScopeGroupedData.entrySet()) {
            String algorithm = algorithmEntry.getKey();
            for (Map.Entry<String, List<CosLongtermPredictOutScaleByAlgorithmDO>> scopeEntry : algorithmEntry.getValue().entrySet()) {
                QueryAlgorithmPredictResultResp.Line line = new QueryAlgorithmPredictResultResp.Line();
                line.setScope(scopeEntry.getKey());
                line.setType("PREDICT");
                line.setAlgorithm(algorithm);
                line.setIncreaseInfos(new ArrayList<>());
                line.setPoints(new ArrayList<>());

                for (CosLongtermPredictOutScaleByAlgorithmDO predictResult : scopeEntry.getValue()) {
                    line.getPoints().add(ListUtils.of(predictResult.getDate(), predictResult.getPredictScale()));
                }

                // 按日期排序
                ListUtils.sortAscNullLast(line.getPoints(), o -> (LocalDate) o.get(0));
                resp.getLines().add(line);
            }
        }

        return true;
    }

}
