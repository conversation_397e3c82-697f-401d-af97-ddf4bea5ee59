package cloud.demand.lab.modules.longterm.cos.entity;

import cloud.demand.lab.common.entity.BaseDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

@Data
@Table("cos_longterm_predict_out_purchase_split")
public class CosLongtermPredictOutPurchaseSplitDO extends BaseDO {

    @Column(value = "task_id")
    private Long taskId;

    @Column(value = "split_version_id")
    private Long splitVersionId;

    /** 策略类型，激进中立保守<br/>Column: [strategy_type] */
    @Column(value = "strategy_type")
    private String strategyType;

    /** 拆分后的日期，这里以月末日期代表当前月份<br/>Column: [date] */
    @Column(value = "date")
    private LocalDate date;

    /** 日期对应的年份 yyyy<br/>Column: [year] */
    @Column(value = "year")
    private Integer year;

    /** 日期对应的月份yyyyMM<br/>Column: [year_month_str] */
    @Column(value = "year_month_str")
    private String yearMonthStr;

    /** 日期对应的季度 1-4<br/>Column: [quarter] */
    @Column(value = "quarter")
    private Integer quarter;

    /** 日期对应的半年 1-2<br/>Column: [half_year] */
    @Column(value = "half_year")
    private Integer halfYear;

    /** 地域名称<br/>Column: [region_name] */
    @Column(value = "region_name")
    private String regionName;

    /** 境内外<br/>Column: [customhouse_title] */
    @Column(value = "customhouse_title")
    private String customhouseTitle;

    /** 是否外部客户，0表示否，1表示是<br/>Column: [is_out_customer] */
    @Column(value = "is_out_customer")
    private Boolean isOutCustomer;

    /** 采购存储量，单位pb，目前采购量等于净增量<br/>Column: [purchase_storage] */
    @Column(value = "purchase_storage")
    private BigDecimal purchaseStorage;

    /** 拆分过程日志<br/>Column: [split_log] */
    @Column(value = "split_log")
    private String splitLog;

}