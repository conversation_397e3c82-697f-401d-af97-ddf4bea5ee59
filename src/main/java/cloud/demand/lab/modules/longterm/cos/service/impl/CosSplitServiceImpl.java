package cloud.demand.lab.modules.longterm.cos.service.impl;

import cloud.demand.lab.common.utils.LoginUtils;
import cloud.demand.lab.modules.longterm.cos.entity.CosLongtermPredictOutSplitVersionDO;
import cloud.demand.lab.modules.longterm.cos.entity.CosLongtermPredictTaskDO;
import cloud.demand.lab.modules.longterm.cos.service.CosSplitService;
import com.pugwoo.dbhelper.DBHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import yunti.boot.exception.BizException;

import javax.annotation.Resource;
import java.time.LocalDateTime;

@Service
@Slf4j
public class CosSplitServiceImpl implements CosSplitService {

    @Resource
    private DBHelper cdLabDbHelper;

    @Override
    @Transactional(value = "cdlabTransactionManager")
    public Long split(Long taskId) {
        // 1. 查询任务信息并创建拆分版本
        CosLongtermPredictTaskDO taskDO = cdLabDbHelper.getByKey(CosLongtermPredictTaskDO.class, taskId);
        if (taskDO == null) {
            throw new BizException("预测任务不存在，taskId: " + taskId);
        }
        log.info("开始拆分任务，taskId: {}, categoryName: {}", taskId, taskDO.getCategoryName());
        
        // 创建拆分版本
        CosLongtermPredictOutSplitVersionDO splitVersionDO = createSplitVersion(taskDO);
        Long splitVersionId = splitVersionDO.getId();
        
        log.info("创建拆分版本成功，splitVersionId: {}", splitVersionId);

        // 2. 计算从当前月份开始到当前半年结束、以及后续每半年的增量


        // 3. 按Prophet算法的月份占比拆分月份的增量


        // 4. 按照最近1年的地域占比拆分


        // 5. 写入拆分结果表中

        return splitVersionId;
    }

    private CosLongtermPredictOutSplitVersionDO createSplitVersion(CosLongtermPredictTaskDO taskDO) {
        CosLongtermPredictOutSplitVersionDO splitVersionDO = new CosLongtermPredictOutSplitVersionDO();
        splitVersionDO.setTaskId(taskDO.getId());
        splitVersionDO.setName("默认拆分版本_" + LocalDateTime.now().toString().substring(0, 19));
        splitVersionDO.setNote("");
        splitVersionDO.setCreator(LoginUtils.getUserName());

        cdLabDbHelper.insert(splitVersionDO);
        return splitVersionDO;
    }
}
