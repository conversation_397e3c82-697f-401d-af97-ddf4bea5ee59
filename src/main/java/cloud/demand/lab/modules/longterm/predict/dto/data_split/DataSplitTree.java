package cloud.demand.lab.modules.longterm.predict.dto.data_split;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.Data;
import lombok.ToString;


/**
 *
 * @param <T> 拆分最明细的数据
 * @param <K> 规则或者历史数据DTO
 *
 */
@Data
@ToString
public class DataSplitTree<T extends SplitValCheckGetter> {
    private DataSplitTreeNode<T> root;

    // root 的根节点，0层只有root， 1-n 是实际每一层的数据
    private List<List<DataSplitTreeNode<T>>> levels;
    // levels 的求和数据， debug 中用到
    private List<BigDecimal> levelsSum;
    // 需要用到的历史数据， 全部打包在这里

    public DataSplitTree(String msg) {
        this.root = new DataSplitTreeNode<>(msg);
        this.levels = new ArrayList<>();
        this.levelsSum = new ArrayList<>();

        List<DataSplitTreeNode<T>> rootLevel = new ArrayList<>();
        rootLevel.add(root);
        levels.add(rootLevel);
        levelsSum.add(BigDecimal.ZERO);
    }

    // 获取最后一层的数据
    public List<DataSplitTreeNode<T>> getLeafLevel() {
        return this.levels.get(this.levels.size() - 1);
    }

    public List<T> getAfterSplitData(){
        return getLeafLevel().stream().map(DataSplitTreeNode::getData).collect(Collectors.toList());
    }

    public void addNode(DataSplitTreeNode<T> parent, DataSplitTreeNode<T> child) {
        parent.addChild(child);
        int parentLevel = parent.getLevel();
        child.setLevel(parent.getLevel()+1);
        if (parentLevel + 1 >= levels.size()) {
            levels.add(new ArrayList<>());
            levelsSum.add(BigDecimal.ZERO);
        }
        int index = parentLevel + 1;
        List<DataSplitTreeNode<T>> selectLevel = levels.get(index);
        selectLevel.add(child);
        this.levelsSum.set(index, this.levelsSum.get(index).add(child.getData().getSplitCheckVal()));
    }

    private int findNodeLevel(DataSplitTreeNode<T> node) {
        for (int i = 0; i < levels.size(); i++) {
            if (levels.get(i).contains(node)) {
                return i;
            }
        }
        return -1; // Node not found
    }
}