package cloud.demand.lab.modules.longterm.predict.dto.args_trend;

import com.pugwoo.dbhelper.annotation.Column;
import java.math.BigDecimal;
import lombok.Data;

@Data
public class YearMonthValueDTO {

    @Column(value = "year_month_str")
    private String yearMonthStr;

    @Column(value = "strategy_type")
    private String strategyType;

    @Column(value = "purchase_core")
    private BigDecimal purchaseCore;
}
