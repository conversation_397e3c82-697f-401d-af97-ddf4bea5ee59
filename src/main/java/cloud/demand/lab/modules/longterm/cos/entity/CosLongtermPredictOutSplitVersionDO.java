package cloud.demand.lab.modules.longterm.cos.entity;

import cloud.demand.lab.common.entity.BaseDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;

@Data
@Table("cos_longterm_predict_out_split_version")
public class CosLongtermPredictOutSplitVersionDO extends BaseDO {

    /** 任务ID<br/>Column: [task_id] */
    @Column(value = "task_id")
    private Long taskId;

    /** 拆分版本名称<br/>Column: [name] */
    @Column(value = "name")
    private String name;

    /** 备注<br/>Column: [note] */
    @Column(value = "note")
    private String note;

    /** 创建人<br/>Column: [creator] */
    @Column(value = "creator")
    private String creator;

}