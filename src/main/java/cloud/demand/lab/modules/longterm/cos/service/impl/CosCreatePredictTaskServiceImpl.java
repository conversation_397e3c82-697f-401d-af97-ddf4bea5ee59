package cloud.demand.lab.modules.longterm.cos.service.impl;

import cloud.demand.lab.common.exception.WrongWebParameterException;
import cloud.demand.lab.common.utils.LoginUtils;
import cloud.demand.lab.modules.longterm.cos.entity.CosLongtermPredictCategoryConfigDO;
import cloud.demand.lab.modules.longterm.cos.entity.CosLongtermPredictInputArgsDO;
import cloud.demand.lab.modules.longterm.cos.entity.CosLongtermPredictInputBigCustomerChangeDO;
import cloud.demand.lab.modules.longterm.cos.entity.CosLongtermPredictInputScaleDO;
import cloud.demand.lab.modules.longterm.cos.entity.CosLongtermPredictOutBigCustomerChangeDO;
import cloud.demand.lab.modules.longterm.cos.entity.CosLongtermPredictOutScaleDO;
import cloud.demand.lab.modules.longterm.cos.entity.CosLongtermPredictTaskDO;
import cloud.demand.lab.modules.longterm.cos.enums.Constants;
import cloud.demand.lab.modules.longterm.cos.service.CosAlgorithmPredictService;
import cloud.demand.lab.modules.longterm.cos.service.CosBigCustomerHistoryChangeService;
import cloud.demand.lab.modules.longterm.cos.service.CosCreatePredictTaskService;
import cloud.demand.lab.modules.longterm.cos.utils.DateRangeUtils;
import cloud.demand.lab.modules.longterm.cos.web.dto.BigCustomerChangeDTO;
import cloud.demand.lab.modules.longterm.cos.web.dto.BigCustomerHistoryChangeDTO;
import cloud.demand.lab.modules.longterm.cos.web.dto.InputArgDateRangeDTO;
import cloud.demand.lab.modules.longterm.cos.web.dto.InputArgsDTO;
import cloud.demand.lab.modules.longterm.cos.web.req.CreatePredictTaskReq;
import cloud.demand.lab.modules.longterm.cos.web.req.QueryBigCustomerHistoryChangeReq;
import cloud.demand.lab.modules.longterm.cos.web.req.QueryCategoryForCreateReq;
import cloud.demand.lab.modules.longterm.cos.web.req.ReCalculateAlgorithmPredictReq;
import cloud.demand.lab.modules.longterm.cos.web.resp.CreatePredictTaskResp;
import cloud.demand.lab.modules.longterm.cos.web.resp.QueryCategoryForCreateResp;
import cloud.demand.lab.modules.longterm.cos.web.resp.ReCalculateAlgorithmPredictResp;
import cloud.demand.lab.modules.longterm.predict.enums.LongtermPredictTaskStatusEnum;
import cloud.demand.lab.modules.longterm.predict.enums.StrategyTypeEnum;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.io.IOUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.net.NetUtils;
import com.pugwoo.wooutils.redis.RedisHelper;
import com.pugwoo.wooutils.redis.Synchronized;
import com.pugwoo.wooutils.string.StringTools;
import com.pugwoo.wooutils.thread.ThreadPoolUtils;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import yunti.boot.exception.BizException;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ThreadPoolExecutor;

@Component
@Slf4j
public class CosCreatePredictTaskServiceImpl implements CosCreatePredictTaskService {

    @Resource
    private DBHelper cdLabDbHelper;
    @Resource
    private RedisHelper redisHelper;
    @Resource
    private CosBigCustomerHistoryChangeService cosBigCustomerHistoryChangeService;
    @Resource
    private CosAlgorithmPredictService cosAlgorithmPredictService;

    protected static final ThreadPoolExecutor updateTaskStatusThreadPool = ThreadPoolUtils.createThreadPool(
            1, 100, 1, "update-cos-task-status");

    @Override
    public QueryCategoryForCreateResp queryCategoryForCreate(QueryCategoryForCreateReq req) {
        List<CosLongtermPredictCategoryConfigDO> categoryConfigs = cdLabDbHelper.getAll(CosLongtermPredictCategoryConfigDO.class);

        QueryCategoryForCreateResp resp = new QueryCategoryForCreateResp();
        resp.setCategoryList(ListUtils.transform(categoryConfigs, o -> {
            QueryCategoryForCreateResp.Item item = new QueryCategoryForCreateResp.Item();
            item.setCategoryId(o.getId());
            item.setCategoryName(o.getCategory());
            LocalDate startDate = DateRangeUtils.getPredictStartDate(o);
            LocalDate endDate = DateRangeUtils.getPredictEndDate(o);
            item.setPredictStart(DateUtils.format(startDate, "yyyy-MM"));
            item.setPredictEnd(DateUtils.format(endDate, "yyyy-MM"));
            item.setInputArgDateRanges(DateRangeUtils.getDateRange(startDate, endDate, o.getIntervalMonth()));
            item.setStrategyTypes(ListUtils.transform(StrategyTypeEnum.values(), QueryCategoryForCreateResp.StrategyType::from));
            return item;
        }));
        return resp;
    }

    @SneakyThrows
    @Override
    @Synchronized(keyScript = "args[0].categoryId", customExceptionMessage = "当前方案正在创建任务中，暂不支持并发给同一方案创建预测任务，请稍后重试")
    @Transactional(value = "cdlabTransactionManager")
    public CreatePredictTaskResp createPredictTask(CreatePredictTaskReq req) {
        // 1. 参数校验
        if (req == null || req.getCategoryId() == null) {
            throw new WrongWebParameterException("方案id(categoryId)不能为空");
        }
        if (req.getInputArgs() == null || req.getInputArgs().isEmpty()) {
            throw new WrongWebParameterException("输入参数(inputArgs)不能为空");
        }

        // 2. 检查方案是否存在
        CosLongtermPredictCategoryConfigDO categoryConfig = getCategoryConfig(req.getCategoryId());

        // 3. 校验输入参数的数量和策略类型
        validateInputArgs(req.getInputArgs(), categoryConfig);

        // 4. 创建COS预测任务
        CosLongtermPredictTaskDO task = createTask(categoryConfig, req.getCategoryId(), req.getIsEnable());

        // 5. 如果预测月份的方案已经isEnable，则覆盖它
        if (req.getIsEnable() != null && req.getIsEnable()) {
            WhereSQL whereSQL = new WhereSQL();
            whereSQL.and("is_enable = ?", true);
            whereSQL.and("category_id = ?", task.getCategoryId());
            whereSQL.and("predict_start = ?", task.getPredictStart());

            cdLabDbHelper.updateAll(CosLongtermPredictTaskDO.class, "is_enable=false",
                    whereSQL.getSQL(), whereSQL.getParams());
            task.setIsEnable(true);
        }

        // 6. 创建任务
        cdLabDbHelper.insert(task);

        // 7. 保存输入参数
        saveInputArgs(req.getInputArgs(), task.getId());
        // 8. 保存大客户历史变化数据（冗余refTaskId对应任务的数据）
        saveBigCustomerHistoryChange(req.getRefTaskId(), req.getCategoryId(), task.getId());
        // 9. 保存大客户未来预测数据
        saveBigCustomerForecast(req.getInputArgs(), task.getId());

        // 10. 发送创建任务消息，等待异步处理
        Runnable sendMsg = () -> redisHelper.send(Constants.REDIS_QUEUE_LONGTERM_PREDICT_TASK_COS,
                task.getId().toString(), 600);
        boolean submit = cdLabDbHelper.executeAfterCommit(sendMsg);
        if (!submit) {
            sendMsg.run();
        }

        CreatePredictTaskResp resp = new CreatePredictTaskResp();
        resp.setTaskId(task.getId());
        return resp;
    }

    @Override
    public CosLongtermPredictCategoryConfigDO getCategoryById(Long id) {
        if (id == null) {
            return null;
        }
        return cdLabDbHelper.getByKey(CosLongtermPredictCategoryConfigDO.class, id);
    }

    @Override
    @Synchronized(keyScript = "args[0]", waitLockMillisecond = 100)
    @Transactional(value = "cdlabTransactionManager")
    public void doRunPredictTask(Long taskId) {
        long start = System.currentTimeMillis();
        log.info("start run cos longterm predict task:{}", taskId);

        // 1. 判断任务的状态，并设置任务状态为运行中
        CosLongtermPredictTaskDO task = getTaskAndSetStatus(taskId);

        try {
            // 2. 重算预测算法结果
            ReCalculateAlgorithmPredictReq calReq = new ReCalculateAlgorithmPredictReq();
            calReq.setCategoryId(task.getCategoryId());
            calReq.setTaskId(taskId);
            ReCalculateAlgorithmPredictResp calResp = cosAlgorithmPredictService.reCalculateAlgorithmPredict(calReq);
            if (!calResp.getIsSuccess()) {
                updateTaskStatus(taskId, LongtermPredictTaskStatusEnum.PREDICT_FAIL,
                        "运行算法测算失败:" + calResp.getErrorMsg());
                return;
            }

            // 3. 查询输入参数
            List<CosLongtermPredictInputArgsDO> inputArgs = cdLabDbHelper.getAll(CosLongtermPredictInputArgsDO.class,
                    "where task_id=?", taskId);
            if (ListUtils.isEmpty(inputArgs)) {
                updateTaskStatus(taskId, LongtermPredictTaskStatusEnum.PREDICT_FAIL, "输入参数为空");
                return;
            }

            // 4. 执行预测计算
            List<CosLongtermPredictOutScaleDO> predictResults = calculatePredictResults(task, inputArgs);

            // 5. 保存预测结果
            if (!ListUtils.isEmpty(predictResults)) {
                // 先删除旧的预测结果
                cdLabDbHelper.delete(CosLongtermPredictOutScaleDO.class, "where task_id = ?", taskId);
                // 插入新的预测结果
                cdLabDbHelper.insertBatchWithoutReturnId(predictResults);
            }

            // 6. 更新任务状态为成功
            long cost = System.currentTimeMillis() - start;
            updateTaskStatusSuccess(taskId, (int) cost);

            log.info("cos longterm predict task completed successfully, taskId:{}, cost:{}ms", taskId, cost);

        } catch (Exception e) {
            log.error("cos longterm predict task failed, taskId:{}", taskId, e);
            updateTaskStatus(taskId, LongtermPredictTaskStatusEnum.PREDICT_FAIL, e.getMessage());
        }
    }


    /**
     * 获取任务并设置状态为运行中
     */
    private CosLongtermPredictTaskDO getTaskAndSetStatus(Long taskId) {
        CosLongtermPredictTaskDO task = cdLabDbHelper.getByKey(CosLongtermPredictTaskDO.class, taskId);
        if (task == null) {
            throw new BizException("cos预测任务" + taskId + "不存在");
        }
        // 只有状态为NEW/FAIL的任务才能执行
        if (!(LongtermPredictTaskStatusEnum.NEW.getCode().equals(task.getTaskStatus())
                || LongtermPredictTaskStatusEnum.PREDICT_FAIL.getName().equals(task.getTaskStatus())
                || LongtermPredictTaskStatusEnum.SPLIT_FAIL.getName().equals(task.getTaskStatus()))) {
            throw new BizException("cos预测任务" + taskId + "状态为" + task.getTaskStatus() + "，不能运行");
        }

        // 设置任务状态为运行中
        task.setTaskStatus(LongtermPredictTaskStatusEnum.RUNNING.getCode());
        task.setTaskStartTime(LocalDateTime.now());
        task.setErrMsg(""); // reset err msg
        try {
            task.setRunIp(StringTools.join(",", NetUtils.getIpv4IPs()));
        } catch (Exception e) {
            task.setRunIp("EXCEPTION:" + e.getMessage());
        }

        updateTaskStatusThreadPool.submit(() -> {
            cdLabDbHelper.update(task);
        });

        return task;
    }

    /**
     * 更新任务状态
     */
    private void updateTaskStatus(Long taskId, LongtermPredictTaskStatusEnum status, String errMsg) {
        CosLongtermPredictTaskDO task = cdLabDbHelper.getByKey(CosLongtermPredictTaskDO.class, taskId);
        if (task != null) {
            task.setTaskStatus(status.getCode());
            task.setTaskEndTime(LocalDateTime.now());
            if (errMsg != null && errMsg.length() > 500) {
                errMsg = errMsg.substring(0, 500); // 限制错误信息长度
            }
            task.setErrMsg(errMsg);
            cdLabDbHelper.update(task);
        }
    }

    /**
     * 更新任务状态为成功
     */
    private void updateTaskStatusSuccess(Long taskId, int costMs) {
        CosLongtermPredictTaskDO task = cdLabDbHelper.getByKey(CosLongtermPredictTaskDO.class, taskId);
        if (task != null) {
            task.setTaskStatus(LongtermPredictTaskStatusEnum.SUCCESS.getCode());
            task.setTaskEndTime(LocalDateTime.now());
            task.setCostMs(costMs);
            task.setErrMsg(null);
            cdLabDbHelper.update(task);
        }
    }

    /**
     * 计算预测结果
     * 根据用户填写的增速参数在存量基础上直接乘以增速算出未来关键日期的存量值
     */
    private List<CosLongtermPredictOutScaleDO> calculatePredictResults(
            CosLongtermPredictTaskDO task,
            List<CosLongtermPredictInputArgsDO> inputArgs) {

        List<CosLongtermPredictOutScaleDO> results = ListUtils.newArrayList();

        // 按strategy分组进行预测
        Map<String, List<CosLongtermPredictInputArgsDO>> inputArgsGroup =
                ListUtils.toMapList(inputArgs, CosLongtermPredictInputArgsDO::getStrategyType, o -> o);

        for (Map.Entry<String, List<CosLongtermPredictInputArgsDO>> entry : inputArgsGroup.entrySet()) {
            List<CosLongtermPredictInputArgsDO> args = entry.getValue();
            ListUtils.sortAscNullLast(args, o -> o.getStartDate());

            if (args.get(0).getStartDate() == null) {
                throw new BizException("预测输入参数的开始时间不能为空");
            }

            // 查询最新的存量数据
            LocalDate scaleLatestDate = args.get(0).getStartDate().minusDays(1);
            List<CosLongtermPredictInputScaleDO> inputScales = cdLabDbHelper.getAll(CosLongtermPredictInputScaleDO.class,
                    "where task_id=? and date=?", task.getId(), scaleLatestDate);
            if (ListUtils.isEmpty(inputScales)) {
                throw new BizException("预测所需要的存量数据不存在");
            }

            List<CosLongtermPredictInputScaleDO> outScales = ListUtils.filter(inputScales,
                    o -> o.getIsOutCustomer() != null && o.getIsOutCustomer().equals(1));
            List<CosLongtermPredictInputScaleDO> inScales = ListUtils.filter(inputScales,
                    o -> o.getIsOutCustomer() != null && o.getIsOutCustomer().equals(0));

            // 开始分内外部的增速进行预测
            BigDecimal currentOuterScale = ListUtils.isEmpty(outScales) ? BigDecimal.ZERO : outScales.get(0).getCurScale();
            BigDecimal currentInnerScale = ListUtils.isEmpty(inScales) ? BigDecimal.ZERO : inScales.get(0).getCurScale();

            for (CosLongtermPredictInputArgsDO inputArg : args) {
                String strategyType = inputArg.getStrategyType();
                LocalDate startDate = inputArg.getStartDate();
                LocalDate endDate = inputArg.getEndDate();

                if (startDate == null || endDate == null) {
                    continue;
                }

                // 处理外部客户预测
                if (inputArg.getScaleGrowthRateOut() != null) {
                    BigDecimal growthRate = inputArg.getScaleGrowthRateOut();
                    // 结束日期的预测值 = 上一周期的存量 * (1 + 增速)
                    BigDecimal predictedScale = currentOuterScale.multiply(BigDecimal.ONE.add(growthRate));
                    predictedScale = predictedScale.setScale(4, RoundingMode.HALF_UP);

                    // 创建预测结果记录
                    CosLongtermPredictOutScaleDO result = new CosLongtermPredictOutScaleDO();
                    result.setTaskId(task.getId());
                    result.setStrategyType(strategyType);
                    result.setDate(endDate);
                    result.setIsOutCustomer(true); // 外部客户
                    result.setPredictScale(predictedScale);
                    results.add(result);

                    // 更新当前外部客户存量为本周期的预测值，供下一周期使用
                    currentOuterScale = predictedScale;
                }

                // 处理内部客户预测
                if (inputArg.getScaleGrowthRateIn() != null) {
                    BigDecimal growthRate = inputArg.getScaleGrowthRateIn();
                    // 结束日期的预测值 = 上一周期的存量 * (1 + 增速)
                    BigDecimal predictedScale = currentInnerScale.multiply(BigDecimal.ONE.add(growthRate));
                    predictedScale = predictedScale.setScale(4, RoundingMode.HALF_UP);

                    // 创建预测结果记录
                    CosLongtermPredictOutScaleDO result = new CosLongtermPredictOutScaleDO();
                    result.setTaskId(task.getId());
                    result.setStrategyType(strategyType);
                    result.setDate(endDate);
                    result.setIsOutCustomer(false); // 内部客户
                    result.setPredictScale(predictedScale);
                    results.add(result);

                    // 更新当前内部客户存量为本周期的预测值，供下一周期使用
                    currentInnerScale = predictedScale;
                }
            }
        }

        return results;
    }

    /**
     * 查找方案信息
     */
    private CosLongtermPredictCategoryConfigDO getCategoryConfig(Long categoryId) {
        if (categoryId == null) {
            throw new WrongWebParameterException("方案id(categoryId)不能为空");
        }
        CosLongtermPredictCategoryConfigDO categoryConfig = cdLabDbHelper.getByKey(CosLongtermPredictCategoryConfigDO.class, categoryId);
        if (categoryConfig == null) {
            throw new WrongWebParameterException("方案不存在");
        }
        return categoryConfig;
    }

    /**
     * 创建COS预测任务
     */
    private CosLongtermPredictTaskDO createTask(CosLongtermPredictCategoryConfigDO categoryConfig,
                                                Long categoryId, Boolean isEnable) {
        CosLongtermPredictTaskDO task = new CosLongtermPredictTaskDO();
        task.setCategoryId(categoryId);
        task.setCategoryName(categoryConfig.getCategory());
        task.setIsEnable(isEnable != null && isEnable);
        task.setParts(categoryConfig.getParts());
        task.setModelPart(categoryConfig.getModelPart());
        task.setIsSeparateInOut(categoryConfig.getIsSeparateInOut());
        task.setTaskStatus(LongtermPredictTaskStatusEnum.NEW.getCode());
        task.setCreator(LoginUtils.getUserNameWithSystem());
        task.setPredictStart(DateRangeUtils.getPredictStartDate(categoryConfig));
        task.setPredictEnd(DateRangeUtils.getPredictEndDate(categoryConfig));
        task.setConditionSql(categoryConfig.getWhereSql());

        try {
            String sql = IOUtils.readClasspathResourceAsString("/sql/longterm_predict/cos/plan_cos_scale.sql");
            sql = sql.replace("${CONDITION}", categoryConfig.getWhereSql());
            task.setInputSql(sql);
        } catch (Exception ignored) {}

        task.setPurchaseInputSql(""); // TODO 要读取sql文件，采购的
        task.setDimsName(categoryConfig.getDimsName());
        task.setScopeCustomer(categoryConfig.getScopeCustomer());
        task.setScopeResourcePool(categoryConfig.getScopeResourcePool());
        task.setIntervalMonth(categoryConfig.getIntervalMonth());
        return task;
    }

    /**
     * 保存输入参数
     */
    private void saveInputArgs(List<InputArgsDTO> inputArgs, Long taskId) {
        if (inputArgs == null || inputArgs.isEmpty()) {
            return;
        }

        List<CosLongtermPredictInputArgsDO> inputArgsDOList = ListUtils.transform(inputArgs, inputArg -> {
            CosLongtermPredictInputArgsDO inputArgsDO = new CosLongtermPredictInputArgsDO();
            inputArgsDO.setTaskId(taskId);
            inputArgsDO.setStrategyType(inputArg.getStrategyType());
            inputArgsDO.setDateName(inputArg.getDateName());
            inputArgsDO.setStartDate(DateUtils.parseLocalDate(inputArg.getStartDate()));
            inputArgsDO.setEndDate(DateUtils.parseLocalDate(inputArg.getEndDate()));
            inputArgsDO.setScaleGrowthRateIn(inputArg.getInnerScaleGrowthRate());
            inputArgsDO.setScaleGrowthRateOut(inputArg.getOuterScaleGrowthRate());
            inputArgsDO.setNote(inputArg.getNote());
            return inputArgsDO;
        });

        cdLabDbHelper.insertBatchWithoutReturnId(inputArgsDOList);
    }

    /**
     * 保存大客户历史变化数据（冗余refTaskId对应任务的数据）
     */
    private void saveBigCustomerHistoryChange(Long refTaskId, Long categoryId, Long newTaskId) {
        if (refTaskId == null) { // 如果没有refTaskId，则查询默认的大客户历史数据
            refTaskId = 0L;
        }

        // 查询大客户历史变化数据
        QueryBigCustomerHistoryChangeReq queryReq = new QueryBigCustomerHistoryChangeReq();
        queryReq.setCategoryId(categoryId);
        queryReq.setTaskId(refTaskId);

        List<BigCustomerHistoryChangeDTO> historyChangeList =
                cosBigCustomerHistoryChangeService.queryBigCustomerHistoryChange(queryReq).getDataList();

        if (historyChangeList != null && !historyChangeList.isEmpty()) {
            List<CosLongtermPredictInputBigCustomerChangeDO> bigCustomerChangeDOList =
                    ListUtils.transform(historyChangeList, historyChange -> {
                CosLongtermPredictInputBigCustomerChangeDO changeDO = historyChange.toDO();
                changeDO.setTaskId(newTaskId); // 设置为新任务的ID
                changeDO.setId(null); // 清空ID，让数据库自动生成
                return changeDO;
            });

            cdLabDbHelper.insertBatchWithoutReturnId(bigCustomerChangeDOList);
        }
    }

    /**
     * 保存大客户未来预测数据
     */
    private void saveBigCustomerForecast(List<InputArgsDTO> inputArgs, Long taskId) {
        if (inputArgs == null || inputArgs.isEmpty()) {
            return;
        }

        List<CosLongtermPredictOutBigCustomerChangeDO> bigCustomerForecastList = new ArrayList<>();

        for (InputArgsDTO inputArg : inputArgs) {
            if (inputArg.getBigCustomerForecast() != null && !inputArg.getBigCustomerForecast().isEmpty()) {
                for (BigCustomerChangeDTO forecast : inputArg.getBigCustomerForecast()) {
                    CosLongtermPredictOutBigCustomerChangeDO forecastDO = new CosLongtermPredictOutBigCustomerChangeDO();
                    forecastDO.setTaskId(taskId);
                    forecastDO.setStrategyType(inputArg.getStrategyType());
                    forecastDO.setCustomerName(forecast.getCustomerName());
                    forecastDO.setIsOutCustomer(forecast.getIsOutCustomer());
                    forecastDO.setStartDate(forecast.getStartDate());
                    forecastDO.setEndDate(forecast.getEndDate());
                    forecastDO.setNetChange(forecast.getNetChange());
                    bigCustomerForecastList.add(forecastDO);
                }
            }
        }

        if (!bigCustomerForecastList.isEmpty()) {
            cdLabDbHelper.insertBatchWithoutReturnId(bigCustomerForecastList);
        }
    }

    /**
     * 校验输入参数的数量和策略类型
     */
    private void validateInputArgs(List<InputArgsDTO> inputArgs, CosLongtermPredictCategoryConfigDO categoryConfig) {
        // 1. 获取该方案的时间输入范围和策略范围
        LocalDate startDate = DateRangeUtils.getPredictStartDate(categoryConfig);
        LocalDate endDate = DateRangeUtils.getPredictEndDate(categoryConfig);
        List<InputArgDateRangeDTO> expectedDateRanges = DateRangeUtils.getDateRange(startDate, endDate, categoryConfig.getIntervalMonth());
        List<String> validStrategyTypes = ListUtils.transform(StrategyTypeEnum.values(), StrategyTypeEnum::getCode);

        // 2. 校验每个输入参数的策略类型是否有效
        for (InputArgsDTO inputArg : inputArgs) {
            if (inputArg.getStrategyType() == null || !validStrategyTypes.contains(inputArg.getStrategyType())) {
                throw new WrongWebParameterException(String.format("输入参数中包含无效的策略类型：%s，有效的策略类型为：%s",
                    inputArg.getStrategyType(), String.join(", ", validStrategyTypes)));
            }
        }

        // 3. 校验每个时间范围和策略类型的组合是否都存在
        Set<String> expectedCombinations = new HashSet<>();
        for (InputArgDateRangeDTO dateRange : expectedDateRanges) {
            for (String strategyType : validStrategyTypes) {
                expectedCombinations.add(dateRange.getDateName() + "_" + strategyType);
            }
        }

        Set<String> actualCombinations = new HashSet<>();
        for (InputArgsDTO inputArg : inputArgs) {
            actualCombinations.add(inputArg.getDateName() + "_" + inputArg.getStrategyType());
        }

        if (!expectedCombinations.equals(actualCombinations)) {
            Set<String> missingCombinations = new HashSet<>(expectedCombinations);
            missingCombinations.removeAll(actualCombinations);
            Set<String> extraCombinations = new HashSet<>(actualCombinations);
            extraCombinations.removeAll(expectedCombinations);

            StringBuilder errorMsg = new StringBuilder("输入参数的时间范围和策略类型组合不匹配：");
            if (!missingCombinations.isEmpty()) {
                errorMsg.append("缺少：").append(String.join(", ", missingCombinations));
            }
            if (!extraCombinations.isEmpty()) {
                if (!missingCombinations.isEmpty()) {
                    errorMsg.append("；");
                }
                errorMsg.append("多余：").append(String.join(", ", extraCombinations));
            }
            throw new WrongWebParameterException(errorMsg.toString());
        }
    }

}
